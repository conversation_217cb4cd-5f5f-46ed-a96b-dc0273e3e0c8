'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { 
  WebSocketConnectionState,
  WebSocketMessage,
  ChatMessage,
  TypingIndicatorMessage,
  FileUploadMessage,
  StatusChangeMessage
} from '@/services/websocketService';
import { getAccessToken } from '@/services/tokenService';
import { useAuth } from '@/components/context/AuthContext';
import { updateChatStatus as updateChatStatusAPI, updateChatPriority } from '@/services/api';

// Helper functions for WebSocket error handling
const getWebSocketErrorMessage = (code: number): string => {
  switch (code) {
    case 4001: return 'Invalid token format';
    case 4002: return 'Token has expired';
    case 4003: return 'Error authenticating token';
    case 4004: return 'No chat ID provided in URL';
    case 4005: return 'Chat ID not found';
    case 4006: return 'Unauthorized access';
    case 4007: return 'Server error';
    default: return `Unknown error (code: ${code})`;
  }
};

const getWebSocketReadyStateText = (readyState: number | undefined): string => {
  switch (readyState) {
    case 0: return 'CONNECTING';
    case 1: return 'OPEN';
    case 2: return 'CLOSING';
    case 3: return 'CLOSED';
    default: return 'UNKNOWN';
  }
};

// Check if WebSocket server is reachable
const checkWebSocketServer = async (wsUrl: string): Promise<boolean> => {
  try {
    // Convert ws:// to http:// for health check
    const healthCheckUrl = wsUrl.replace('ws://', 'http://').replace('wss://', 'https://').replace('/ws/support/', '/health');
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    const response = await fetch(healthCheckUrl, {
      method: 'GET',
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.warn('WebSocket server health check failed:', error);
    return false;
  }
};

// Chat connection info
interface ChatConnection {
  socket: WebSocket | null;
  state: WebSocketConnectionState;
  chatId: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'resolved';
  lastActivity: Date;
  reconnectAttempts: number;
  pingInterval: NodeJS.Timeout | null;
  autoConnected?: boolean; // Flag to indicate if connection was made automatically
}

// Message with enhanced metadata
interface ChatMessageWithMetadata extends ChatMessage {
  chat_id: number;
  unread?: boolean;
  local_id?: string; // For optimistic updates
}

// Typing user info
interface TypingUser {
  user_id: string;
  user_name: string;
  timestamp: Date;
}

// Upload progress info
interface UploadProgress {
  message_id: string;
  progress: number;
  file_name: string;
}

// Admin WebSocket Context value
interface AdminWebSocketContextValue {
  // Connection management
  activeConnections: Map<string, ChatConnection>;  connectToChat: (chatId: string, options?: {
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    status?: 'pending' | 'in_progress' | 'resolved';
    autoConnected?: boolean;
  }) => Promise<void>;
  disconnectFromChat: (chatId: string) => void;
  disconnectAll: () => void;
  getConnectionState: (chatId: string) => WebSocketConnectionState;
  
  // Message handling
  messages: Map<string, ChatMessageWithMetadata[]>;
  sendMessage: (chatId: string, message: string) => void;
  sendTypingStart: (chatId: string) => void;
  sendTypingStop: (chatId: string) => void;
  
  // Real-time features
  typingUsers: Map<string, TypingUser[]>;
  uploadProgress: Map<string, Map<string, UploadProgress>>;
  
  // Chat management
  prioritizeChat: (chatId: string, priority: 'low' | 'medium' | 'high' | 'urgent') => void;
  updateChatStatus: (chatId: string, status: 'pending' | 'in_progress' | 'resolved') => void;
  
  // Unread counts
  unreadCounts: Map<string, number>;
  markAsRead: (chatId: string) => void;
    // Auto-connection features (disabled by default to prevent loops)
  autoConnectEnabled: boolean;
  setAutoConnectEnabled: (enabled: boolean) => void;
  maxConnections: number;
  setMaxConnections: (max: number) => void;
}

const AdminWebSocketContext = createContext<AdminWebSocketContextValue>({
  activeConnections: new Map(),
  connectToChat: async () => {},
  disconnectFromChat: () => {},
  disconnectAll: () => {},
  getConnectionState: () => 'disconnected',
  messages: new Map(),
  sendMessage: () => {},
  sendTypingStart: () => {},
  sendTypingStop: () => {},
  typingUsers: new Map(),
  uploadProgress: new Map(),
  prioritizeChat: () => {},
  updateChatStatus: () => {},
  unreadCounts: new Map(),
  markAsRead: () => {},
  autoConnectEnabled: false, // Disabled by default to prevent loops
  setAutoConnectEnabled: () => {},
  maxConnections: 8,
  setMaxConnections: () => {},
});

export const AdminWebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ 
  children
}) => {
  // Get auth context for admin verification
  const { isAuthenticated, isAdmin} = useAuth();
  
  // State management
  const [activeConnections, setActiveConnections] = useState<Map<string, ChatConnection>>(new Map());
  const [messages, setMessages] = useState<Map<string, ChatMessageWithMetadata[]>>(new Map());
  const [typingUsers, setTypingUsers] = useState<Map<string, TypingUser[]>>(new Map());
  const [uploadProgress, setUploadProgress] = useState<Map<string, Map<string, UploadProgress>>>(new Map());
  const [unreadCounts, setUnreadCounts] = useState<Map<string, number>>(new Map());  const [autoConnectEnabled, setAutoConnectEnabled] = useState(false); // Disabled by default
  const [maxConnections, setMaxConnections] = useState(8);
  
  // Refs for stable references
  const connectionsRef = useRef<Map<string, ChatConnection>>(new Map());
  const messagesRef = useRef<Map<string, ChatMessageWithMetadata[]>>(new Map());
  const typingTimeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  
  // Create reconnection handler reference that will be set later
  const reconnectionHandlerRef = useRef<((chatId: string) => void) | null>(null);
  
  // Check if running on client side
  const isClient = typeof window !== 'undefined';

  // Update connection state
  const updateConnectionState = useCallback((chatId: string, state: WebSocketConnectionState) => {
    setActiveConnections(prev => {
      const newConnections = new Map(prev);
      const connection = newConnections.get(chatId);
      
      if (connection) {
        connection.state = state;
        newConnections.set(chatId, connection);
        connectionsRef.current = newConnections;
      }
      
      return newConnections;
    });
  }, []);

  // Handle chat messages
  const handleChatMessage = useCallback((chatId: string, message: ChatMessage) => {
    const messageWithMetadata: ChatMessageWithMetadata = {
      ...message,
      chat_id: parseInt(chatId),
      unread: message.sender === 'user', // Mark user messages as unread by default
    };

    setMessages(prev => {
      const newMessages = new Map(prev);
      const chatMessages = newMessages.get(chatId) || [];
      
      // Check for duplicates
      const exists = chatMessages.some(msg => msg.id === message.id);
      if (!exists) {
        const updatedMessages = [...chatMessages, messageWithMetadata].slice(-100); // Keep last 100 messages
        newMessages.set(chatId, updatedMessages);
        messagesRef.current = newMessages;
      }
      
      return newMessages;
    });

    // Update unread count if it's a user message
    if (message.sender === 'user') {
      setUnreadCounts(prev => {
        const newCounts = new Map(prev);
        newCounts.set(chatId, (newCounts.get(chatId) || 0) + 1);
        return newCounts;
      });
    }
  }, []);

  // Handle typing indicators
  const handleTypingIndicator = useCallback((chatId: string, data: TypingIndicatorMessage) => {
    if (!data.user_id || !data.user_name) return;

    const isTyping = data.type === 'typing.start' || (data.type === 'typing.indicator' && data.is_typing);
    
    setTypingUsers(prev => {
      const newTypingUsers = new Map(prev);
      const chatTypingUsers = newTypingUsers.get(chatId) || [];
      
      if (isTyping) {
        // Add or update typing user
        const existingIndex = chatTypingUsers.findIndex(user => user.user_id === data.user_id);
        const typingUser: TypingUser = {
          user_id: data.user_id!,
          user_name: data.user_name!,
          timestamp: new Date(),
        };
        
        if (existingIndex >= 0) {
          chatTypingUsers[existingIndex] = typingUser;
        } else {
          chatTypingUsers.push(typingUser);
        }
        
        // Set timeout to remove typing indicator
        const timeoutKey = `${chatId}-${data.user_id}`;
        if (typingTimeoutsRef.current.has(timeoutKey)) {
          clearTimeout(typingTimeoutsRef.current.get(timeoutKey));
        }
        
        const timeout = setTimeout(() => {
          setTypingUsers(currentPrev => {
            const currentNew = new Map(currentPrev);
            const currentChatUsers = currentNew.get(chatId) || [];
            const filteredUsers = currentChatUsers.filter(user => user.user_id !== data.user_id!);
            
            if (filteredUsers.length > 0) {
              currentNew.set(chatId, filteredUsers);
            } else {
              currentNew.delete(chatId);
            }
            
            return currentNew;
          });
          typingTimeoutsRef.current.delete(timeoutKey);
        }, 5000);
        
        typingTimeoutsRef.current.set(timeoutKey, timeout);
      } else {
        // Remove typing user
        const filteredUsers = chatTypingUsers.filter(user => user.user_id !== data.user_id!);
        
        if (filteredUsers.length > 0) {
          newTypingUsers.set(chatId, filteredUsers);
        } else {
          newTypingUsers.delete(chatId);
        }
      }
      
      return newTypingUsers;
    });
  }, []);

  // Handle file upload progress
  const handleFileUpload = useCallback((chatId: string, data: FileUploadMessage) => {
    if (data.type === 'upload.progress' && data.message_id && data.progress !== undefined) {
      setUploadProgress(prev => {
        const newProgress = new Map(prev);
        const chatProgress = newProgress.get(chatId) || new Map();
        
        chatProgress.set(data.message_id!, {
          message_id: data.message_id!,
          progress: data.progress!,
          file_name: data.file_info?.file_name || 'Unknown file',
        });
        
        newProgress.set(chatId, chatProgress);
        return newProgress;
      });
    } else if (data.type === 'file.uploaded' && data.message_id) {
      // Remove from progress when complete
      setUploadProgress(prev => {
        const newProgress = new Map(prev);
        const chatProgress = newProgress.get(chatId);
        
        if (chatProgress) {
          chatProgress.delete(data.message_id!);
          if (chatProgress.size === 0) {
            newProgress.delete(chatId);
          } else {
            newProgress.set(chatId, chatProgress);
          }
        }
        
        return newProgress;
      });
    }
  }, []);

  // Handle status changes
  const handleStatusChange = useCallback((chatId: string, data: StatusChangeMessage) => {
    setActiveConnections(prev => {
      const newConnections = new Map(prev);
      const connection = newConnections.get(chatId);
      
      if (connection) {
        connection.status = data.status;
        if (data.priority) {
          connection.priority = data.priority;
        }
        newConnections.set(chatId, connection);
        connectionsRef.current = newConnections;
      }
      
      return newConnections;
    });
  }, []);

  // Handle incoming WebSocket messages
  const handleWebSocketMessage = useCallback((chatId: string, data: WebSocketMessage) => {
    switch (data.type) {
      case 'chat.message':
        handleChatMessage(chatId, data as unknown as ChatMessage);
        break;
      case 'typing.start':
      case 'typing.stop':
      case 'typing.indicator':
        handleTypingIndicator(chatId, data as unknown as TypingIndicatorMessage);
        break;
      case 'file.uploaded':
      case 'upload.progress':
        handleFileUpload(chatId, data as unknown as FileUploadMessage);
        break;
      case 'status.change':
        handleStatusChange(chatId, data as unknown as StatusChangeMessage);
        break;
      case 'pong':
        // Handle pong response
        break;
      default:
        console.log(`Unhandled message type for chat ${chatId}:`, data.type);
    }
  }, [handleChatMessage, handleTypingIndicator, handleFileUpload, handleStatusChange]);

  // Admin-only feature - return disconnected connection for non-admin users
  const createWebSocketConnection = useCallback(async (
    chatId: string, 
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium',
    status: 'pending' | 'in_progress' | 'resolved' = 'pending',
    autoConnected: boolean = false
  ): Promise<ChatConnection> => {
    if (!isAuthenticated || !isAdmin) {
      console.warn('WebSocket connections are restricted to admin users');
      return {
        socket: null,
        state: 'disconnected',
        chatId,
        priority,
        status,
        lastActivity: new Date(),
        reconnectAttempts: 0,
        pingInterval: null,
        autoConnected,
      };
    }

    if (!isClient) {
      return {
        socket: null,
        state: 'disconnected',
        chatId,
        priority,
        status,
        lastActivity: new Date(),
        reconnectAttempts: 0,
        pingInterval: null,
        autoConnected,
      };
    }

    const token = getAccessToken();
    if (!token) {
      console.error('Cannot create WebSocket: No authentication token');
      return {
        socket: null,
        state: 'error',
        chatId,
        priority,
        status,
        lastActivity: new Date(),
        reconnectAttempts: 0,
        pingInterval: null,
        autoConnected,
      };
    }    const baseWsUrl = process.env.NEXT_PUBLIC_WS_URL;
    if (!baseWsUrl) {
      console.error('WebSocket URL not configured - Please set NEXT_PUBLIC_WS_URL environment variable');
      return {
        socket: null,
        state: 'error',
        chatId,
        priority,
        status,
        lastActivity: new Date(),
        reconnectAttempts: 0,
        pingInterval: null,
        autoConnected,
      };
    }

    // Validate WebSocket URL format
    try {
      new URL(baseWsUrl.replace('ws://', 'http://').replace('wss://', 'https://'));
    } catch {
      console.error('Invalid WebSocket URL format:', baseWsUrl);
      return {
        socket: null,
        state: 'error',
        chatId,
        priority,
        status,
        lastActivity: new Date(),
        reconnectAttempts: 0,
        pingInterval: null,
        autoConnected,
      };
    }

    // Pre-check: Verify WebSocket server is reachable
    const isServerReachable = await checkWebSocketServer(baseWsUrl);
    if (!isServerReachable) {
      console.warn(`⚠️ WebSocket server appears to be unreachable at ${baseWsUrl}`);
      
      // Still attempt connection but with user notification
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('websocket-server-unreachable', {
          detail: { 
            message: 'Chat server may be temporarily unavailable. Connection will be attempted anyway.',
            serverUrl: baseWsUrl
          }
        }));
      }
    }

    const wsUrl = `${baseWsUrl}/ws/support/${chatId}/?token=${token}`;
    
    let socket: WebSocket;
    try {
      socket = new WebSocket(wsUrl);
    } catch (error) {
      console.error('❌ Failed to create WebSocket:', error);
      return {
        socket: null,
        state: 'error',
        chatId,
        priority,
        status,
        lastActivity: new Date(),
        reconnectAttempts: 0,
        pingInterval: null,
        autoConnected,
      };
    }
    
    const connection: ChatConnection = {
      socket,
      state: 'connecting',
      chatId,
      priority,
      status,
      lastActivity: new Date(),
      reconnectAttempts: 0,
      pingInterval: null,
      autoConnected,
    };

    // Connection timeout handler
    const connectionTimeout = setTimeout(() => {
      if (socket.readyState === WebSocket.CONNECTING) {
        console.warn(`WebSocket connection timeout for chat ${chatId}`);
        socket.close();
        connection.state = 'error';
        updateConnectionState(chatId, 'error');
      }
    }, 10000); // 10 second timeout

    // WebSocket event handlers
    socket.onopen = () => {
      clearTimeout(connectionTimeout);
      console.log(`✅ Connected to chat ${chatId}`);
      connection.state = 'connected';
      connection.reconnectAttempts = 0;
      connection.lastActivity = new Date();
      
      // Start ping interval
      connection.pingInterval = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          try {
            socket.send(JSON.stringify({ type: 'ping' }));
          } catch (error) {
            console.error('Failed to send ping:', error);
          }
        }
      }, 30000);
      
      updateConnectionState(chatId, 'connected');
    };

    socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data) as WebSocketMessage;
        connection.lastActivity = new Date();
        
        handleWebSocketMessage(chatId, data);
      } catch (error) {
        console.error(`Error processing message for chat ${chatId}:`, error);
      }
    };    socket.onclose = (event) => {
      clearTimeout(connectionTimeout);
      if (connection.pingInterval) {
        clearInterval(connection.pingInterval);
        connection.pingInterval = null;
      }
      
      const isNormalClosure = event.code === 1000 || event.code === 1001;
      const isAuthError = event.code >= 4001 && event.code <= 4007;
      
      if (isNormalClosure) {
        console.log(`✅ Chat ${chatId} connection closed normally`);
        connection.state = 'disconnected';
        updateConnectionState(chatId, 'disconnected');
      } else if (isAuthError) {
        console.error(`❌ Chat ${chatId} authentication error:`, {
          code: event.code,
          reason: event.reason || getWebSocketErrorMessage(event.code),
          chatId
        });
        connection.state = 'error';
        updateConnectionState(chatId, 'error');
      } else {
        console.warn(`⚠️ Chat ${chatId} connection closed unexpectedly:`, {
          code: event.code,
          reason: event.reason,
          wasClean: event.wasClean,
          chatId
        });
        connection.state = 'disconnected';
        updateConnectionState(chatId, 'disconnected');
          // Only attempt reconnection for unexpected closures and if under retry limit
        if (connection.reconnectAttempts < 5) {
          console.log(`🔄 Scheduling reconnection attempt ${connection.reconnectAttempts + 1} for chat ${chatId}`);
          setTimeout(() => {
            if (connectionsRef.current.has(chatId) && reconnectionHandlerRef.current) {
              reconnectionHandlerRef.current(chatId);
            }
          }, Math.min(1000 * Math.pow(2, connection.reconnectAttempts), 30000));
        } else {
          console.error(`❌ Maximum reconnection attempts reached for chat ${chatId}`);
          connection.state = 'error';
          updateConnectionState(chatId, 'error');
        }
      }
    };

    socket.onerror = (error) => {
      clearTimeout(connectionTimeout);
      const errorDetails = {
        type: error.type,
        target: (error.target as WebSocket)?.url || wsUrl,
        readyState: socket?.readyState,
        readyStateText: getWebSocketReadyStateText(socket?.readyState),
        timestamp: new Date().toISOString(),
        chatId,
        reconnectAttempts: connection.reconnectAttempts,
        priority: connection.priority
      };

      console.error(`❌ WebSocket error for chat ${chatId}:`, errorDetails);
      
      // Check if this is a connection failure (readyState 0 = CONNECTING)
      if (socket?.readyState === 0) {
        console.error(`🚫 Failed to establish WebSocket connection to chat ${chatId}. Server may be unavailable.`);
        
        // Show user-friendly message for connection failures
        if (typeof window !== 'undefined') {
          // Only show toast if we haven't already shown one for this chat recently
          const lastErrorTime = localStorage.getItem(`ws_error_${chatId}`);
          const now = Date.now();
          if (!lastErrorTime || now - parseInt(lastErrorTime) > 60000) { // 1 minute cooldown
            localStorage.setItem(`ws_error_${chatId}`, now.toString());
          }
        }
      }
      
      connection.state = 'error';
      updateConnectionState(chatId, 'error');
    };
    return connection;
  }, [isAuthenticated, isAdmin, isClient, updateConnectionState, handleWebSocketMessage]);
  // Attempt reconnection
  const attemptReconnection = useCallback(async (chatId: string) => {
    const connection = connectionsRef.current.get(chatId);
    if (!connection || connection.reconnectAttempts >= 5) return;

    connection.reconnectAttempts++;
    connection.state = 'reconnecting';
    updateConnectionState(chatId, 'reconnecting');

    console.log(`🔄 Attempting reconnection ${connection.reconnectAttempts}/5 for chat ${chatId}`);

    try {
      const newConnection = await createWebSocketConnection(
        chatId, 
        connection.priority, 
        connection.status, 
        connection.autoConnected
      );
      
      setActiveConnections(prev => {
        const newConnections = new Map(prev);
        newConnections.set(chatId, newConnection);
        connectionsRef.current = newConnections;
        return newConnections;
      });
    } catch (error) {
      console.error(`❌ Reconnection attempt ${connection.reconnectAttempts} failed for chat ${chatId}:`, error);
      
      // If this was the last attempt, mark as error
      if (connection.reconnectAttempts >= 5) {
        connection.state = 'error';
        updateConnectionState(chatId, 'error');
      }
    }
  }, [updateConnectionState, createWebSocketConnection]);

  // Set the reconnection handler reference
  React.useEffect(() => {
    reconnectionHandlerRef.current = attemptReconnection;
  }, [attemptReconnection]);

  // Disconnect from chat
  const disconnectFromChat = useCallback((chatId: string) => {
    if (!isAdmin) {
      console.warn('Only admin users can disconnect from chats');
      return;
    }

    const connection = activeConnections.get(chatId);
    if (!connection) return;

    if (connection.socket) {
      connection.socket.close();
    }
    
    if (connection.pingInterval) {
      clearInterval(connection.pingInterval);
    }

    setActiveConnections(prev => {
      const newConnections = new Map(prev);
      newConnections.delete(chatId);
      connectionsRef.current = newConnections;
      return newConnections;
    });
  }, [isAdmin, activeConnections]);

  // Connect to chat
  const connectToChat = useCallback(async (
    chatId: string, 
    options?: {
      priority?: 'low' | 'medium' | 'high' | 'urgent';
      status?: 'pending' | 'in_progress' | 'resolved';
      autoConnected?: boolean;
    }
  ) => {
    if (!isAdmin) {
      console.warn('Only admin users can use multi-chat connections');
      return;
    }

    const { priority = 'medium', status = 'pending', autoConnected = false } = options || {};

    // Check if already connected
    if (activeConnections.has(chatId)) {
      console.log(`Already connected to chat ${chatId}`);
      return;
    }

    // Check connection limits
    if (activeConnections.size >= maxConnections) {
      // Disconnect lowest priority chat, preferring manually connected chats
      const sortedConnections = Array.from(activeConnections.entries())
        .sort((a, b) => {
          const priorityOrder = { low: 0, medium: 1, high: 2, urgent: 3 };
          const aPriority = priorityOrder[a[1].priority];
          const bPriority = priorityOrder[b[1].priority];
          
          // Factor in auto-connection status (prefer keeping auto-connected high priority chats)
          const aScore = aPriority + (a[1].autoConnected ? 0.5 : 0);
          const bScore = bPriority + (b[1].autoConnected ? 0.5 : 0);
          
          if (aScore !== bScore) {
            return aScore - bScore; // Lower score first (lower priority, manual connections)
          }
          
          return a[1].lastActivity.getTime() - b[1].lastActivity.getTime(); // Older first
        });
      
      if (sortedConnections.length > 0) {
        const [oldestChatId] = sortedConnections[0];
        console.log(`Disconnecting chat ${oldestChatId} to make room for ${chatId}`);
        disconnectFromChat(oldestChatId);
      }
    }

    try {
      const connection = await createWebSocketConnection(chatId, priority, status, autoConnected);
      setActiveConnections(prev => {
        const newConnections = new Map(prev);
        newConnections.set(chatId, connection);
        connectionsRef.current = newConnections;
        return newConnections;
      });    } catch (error) {
      console.error(`Failed to connect to chat ${chatId}:`, error);
      
      // Dispatch error event for UI handling
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('websocket-connection-error', {
          detail: { 
            chatId, 
            error: error instanceof Error ? error.message : 'Failed to establish connection',
            priority
          }
        }));
      }
    }
  }, [isAdmin, activeConnections, maxConnections, createWebSocketConnection, disconnectFromChat]);

  // Disconnect all chats
  const disconnectAll = useCallback(() => {
    if (!isAdmin) {
      console.warn('Only admin users can disconnect all chats');
      return;
    }

    activeConnections.forEach((_, chatId) => {
      disconnectFromChat(chatId);
    });
  }, [isAdmin, activeConnections, disconnectFromChat]);

  // Get connection state
  const getConnectionState = useCallback((chatId: string): WebSocketConnectionState => {
    return activeConnections.get(chatId)?.state || 'disconnected';
  }, [activeConnections]);

  // Send message
  const sendMessage = useCallback((chatId: string, message: string) => {
    if (!isAdmin) {
      console.warn('Only admin users can send messages');
      return;
    }

    const connection = activeConnections.get(chatId);
    if (!connection || !connection.socket || connection.socket.readyState !== WebSocket.OPEN) {
      console.error(`Cannot send message to chat ${chatId}: Not connected`);
      return;
    }

    const messageData = {
      type: 'chat.message',
      message: message.trim(),
      timestamp: new Date().toISOString(),
    };

    connection.socket.send(JSON.stringify(messageData));
  }, [isAdmin, activeConnections]);

  // Send typing indicators
  const sendTypingStart = useCallback((chatId: string) => {
    if (!isAdmin) {
      console.warn('Only admin users can send typing indicators');
      return;
    }

    const connection = activeConnections.get(chatId);
    if (!connection || !connection.socket || connection.socket.readyState !== WebSocket.OPEN) return;

    connection.socket.send(JSON.stringify({ type: 'typing.start' }));
  }, [isAdmin, activeConnections]);

  const sendTypingStop = useCallback((chatId: string) => {
    if (!isAdmin) {
      console.warn('Only admin users can send typing indicators');
      return;
    }

    const connection = activeConnections.get(chatId);
    if (!connection || !connection.socket || connection.socket.readyState !== WebSocket.OPEN) return;

    connection.socket.send(JSON.stringify({ type: 'typing.stop' }));
  }, [isAdmin, activeConnections]);

  // Update chat priority with REST API integration
  const prioritizeChat = useCallback(async (chatId: string, priority: 'low' | 'medium' | 'high' | 'urgent') => {
    if (!isAdmin) {
      console.warn('prioritizeChat: Admin access required');
      return;
    }

    try {
      // Store previous priority for rollback
      const connection = connectionsRef.current.get(chatId);
      const previousPriority = connection?.priority;

      // Optimistically update local state first
      setActiveConnections(prev => {
        const newConnections = new Map(prev);
        const connection = newConnections.get(chatId);
        
        if (connection) {
          connection.priority = priority;
          newConnections.set(chatId, connection);
          connectionsRef.current = newConnections;
        }
        
        return newConnections;
      });

      // Call REST API to update priority
      const response = await updateChatPriority(parseInt(chatId), priority);
      
      if (response.status !== 200) {
        // Rollback optimistic update on API failure
        setActiveConnections(prev => {
          const newConnections = new Map(prev);
          const connection = newConnections.get(chatId);
          
          if (connection && previousPriority) {
            connection.priority = previousPriority;
            newConnections.set(chatId, connection);
            connectionsRef.current = newConnections;
          }
          
          return newConnections;
        });
        
        console.error('Failed to update chat priority:', response.data);
      }
    } catch (error) {
      console.error('Error updating chat priority:', error);
      
      // Rollback optimistic update on error
      const connection = connectionsRef.current.get(chatId);
      const previousPriority = connection?.priority;
      
      setActiveConnections(prev => {
        const newConnections = new Map(prev);
        const connection = newConnections.get(chatId);
        
        if (connection && previousPriority) {
          connection.priority = previousPriority;
          newConnections.set(chatId, connection);
          connectionsRef.current = newConnections;
        }
        
        return newConnections;
      });
    }
  }, [isAdmin]);

  // Update chat status with REST API integration
  const updateChatStatus = useCallback(async (chatId: string, status: 'pending' | 'in_progress' | 'resolved') => {
    if (!isAdmin) {
      console.warn('updateChatStatus: Admin access required');
      return;
    }

    try {
      // Store previous status for rollback
      const connection = connectionsRef.current.get(chatId);
      const previousStatus = connection?.status;

      // Optimistically update local state first
      setActiveConnections(prev => {
        const newConnections = new Map(prev);
        const connection = newConnections.get(chatId);
        
        if (connection) {
          connection.status = status;
          newConnections.set(chatId, connection);
          connectionsRef.current = newConnections;
        }
        
        return newConnections;
      });

      // Call existing REST API function to update status
      const response = await updateChatStatusAPI(parseInt(chatId), status);
      
      if (response.status !== 200) {
        // Rollback optimistic update on API failure
        setActiveConnections(prev => {
          const newConnections = new Map(prev);
          const connection = newConnections.get(chatId);
          
          if (connection && previousStatus) {
            connection.status = previousStatus;
            newConnections.set(chatId, connection);
            connectionsRef.current = newConnections;
          }
          
          return newConnections;
        });
        
        console.error('Failed to update chat status:', response.data);
      } else {
        // Broadcast status change to other components
        window.dispatchEvent(new CustomEvent('chat-status-updated', {
          detail: { chatId: parseInt(chatId), status }
        }));
      }
    } catch (error) {
      console.error('Error updating chat status:', error);
      
      // Rollback optimistic update on error
      const connection = connectionsRef.current.get(chatId);
      const previousStatus = connection?.status;
      
      setActiveConnections(prev => {
        const newConnections = new Map(prev);
        const connection = newConnections.get(chatId);
        
        if (connection && previousStatus) {
          connection.status = previousStatus;
          newConnections.set(chatId, connection);
          connectionsRef.current = newConnections;
        }
        
        return newConnections;
      });
    }
  }, [isAdmin]);

  // Mark chat as read
  const markAsRead = useCallback((chatId: string) => {
    setUnreadCounts(prev => {
      const newCounts = new Map(prev);
      newCounts.delete(chatId);
      return newCounts;
    });

    // Mark messages as read
    setMessages(prev => {
      const newMessages = new Map(prev);
      const chatMessages = newMessages.get(chatId);
      
      if (chatMessages) {
        const updatedMessages = chatMessages.map(msg => ({ ...msg, unread: false }));
        newMessages.set(chatId, updatedMessages);
        messagesRef.current = newMessages;
      }
          return newMessages;
    });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    // Capture current ref values when effect runs
    const timeouts = typingTimeoutsRef.current;
    
    return () => {
      // Clear all timeouts
      timeouts.forEach(timeout => clearTimeout(timeout));
      timeouts.clear();
      
      // Disconnect all chats
      disconnectAll();
    };
  }, [disconnectAll]);

  const contextValue: AdminWebSocketContextValue = {
    activeConnections,
    connectToChat,
    disconnectFromChat,
    disconnectAll,
    getConnectionState,
    messages,
    sendMessage,
    sendTypingStart,
    sendTypingStop,
    typingUsers,
    uploadProgress,
    prioritizeChat,
    updateChatStatus,
    unreadCounts,
    markAsRead,
    autoConnectEnabled,
    setAutoConnectEnabled,
    maxConnections,
    setMaxConnections,
  };
  return (
    <AdminWebSocketContext.Provider value={contextValue}>
      {children}
    </AdminWebSocketContext.Provider>
  );
};

// Custom hook to use the admin WebSocket context
export const useAdminWebSocket = () => {
  const context = useContext(AdminWebSocketContext);
  if (context === undefined) {
    throw new Error('useAdminWebSocket must be used within an AdminWebSocketProvider');
  }
  return context;
};
