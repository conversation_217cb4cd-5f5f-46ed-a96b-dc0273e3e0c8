/* Message Status Animations and Styles */

/* Status icon containers */
.status-icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
  position: relative;
  width: 16px;
  height: 16px;
}

/* Message status animations */
@keyframes statusSending {
  0% { transform: scale(0.8); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(0.8); opacity: 0.5; }
}

@keyframes statusSent {
  0% { transform: scale(0); opacity: 0; }
  70% { transform: scale(1.2); opacity: 0.9; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes statusDelivered {
  0% { transform: translateX(-5px); opacity: 0; }
  60% { transform: translateX(2px); opacity: 0.7; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes statusRead {
  0% { color: #133157; }
  30% { color: #febd49; }
  100% { color: #10B981; }
}

/* Status icon styling */
.status-icon-sending {
  color: rgba(19, 49, 87, 0.5);
  animation: statusSending 1.5s infinite;
}

.status-icon-sent {
  color: #133157;
  animation: statusSent 0.5s ease-out;
}

.status-icon-delivered {
  color: #133157;
  animation: statusDelivered 0.5s ease-out;
}

.status-icon-read {
  color: #10B981;
  animation: statusRead 1s ease-out;
}

/* Status text styling */
.status-text {
  font-size: 0.65rem;
  margin-left: 4px;
  opacity: 0.75;
  transition: all 0.2s ease;
}

.message-container:hover .status-text {
  opacity: 1;
}

/* Tooltip for message status */
.status-tooltip {
  position: absolute;
  background: rgba(19, 49, 87, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.65rem;
  bottom: 100%;
  right: 0;
  white-space: nowrap;
  pointer-events: none;
  opacity: 0;
  transform: translateY(5px);
  transition: all 0.2s ease;
  z-index: 10;
}

.status-icon-container:hover .status-tooltip {
  opacity: 1;
  transform: translateY(0);
}

/* Dark mode */
.dark .status-icon-sending {
  color: rgba(224, 224, 224, 0.5);
}

.dark .status-icon-sent,
.dark .status-icon-delivered {
  color: #e0e0e0;
}

.dark .status-icon-read {
  color: #34d399;
}

.dark .status-tooltip {
  background: rgba(31, 41, 55, 0.95);
}
