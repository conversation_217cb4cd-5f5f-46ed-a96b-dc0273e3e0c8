{"name": "support", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.12.2", "js-cookie": "^3.0.5", "lottie-react": "^2.4.1", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.7", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "critters": "^0.0.23", "css-loader": "^7.1.2", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "style-loader": "^4.0.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "^5"}}