/* Enhanced attachment preview styles */
.attachment-preview-image {
  position: relative;
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  margin: 0.5rem 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.attachment-preview-image:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.attachment-preview-image img {
  width: auto;
  max-width: 300px;
  max-height: 200px;
  border-radius: 12px;
  object-fit: cover;
}

.attachment-link {
  display: inline-block;
  text-decoration: none;
  color: inherit;
  margin-right: 0.75rem;
  margin-bottom: 0.5rem;
  transition: transform 0.2s ease;
}

.attachment-link:hover {
  transform: translateY(-2px);
}

.attachment-file {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  gap: 0.5rem;
  background: rgba(19, 49, 87, 0.08);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.attachment-file:hover {
  background: rgba(19, 49, 87, 0.12);
}

.attachment-filename {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.75rem;
  font-weight: 500;
}

.attachment-pdf {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.attachment-pdf:hover {
  background: rgba(244, 67, 54, 0.15);
}

.attachment-document {
  background: rgba(25, 118, 210, 0.1);
  color: #1976d2;
}

.attachment-document:hover {
  background: rgba(25, 118, 210, 0.15);
}

/* Image lightbox effect */
.image-lightbox {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
}

.image-lightbox-content {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
}

.image-lightbox-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.image-lightbox-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}
