'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageSquare,  
  Settings,
  X,
} from 'lucide-react';
import { useAdminWebSocket } from '@/components/context/AdminWebSocketContext';
import { useChatData } from '@/components/context/ChatDataContext';
import { WebSocketIndicator } from '@/components/ui/websocket-indicator';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { SupportChat } from './SupportChatList';

interface AdminChatDashboardProps {
  onChatSelect?: (chat: SupportChat) => void;
}

export const AdminChatDashboard: React.FC<AdminChatDashboardProps> = ({
  onChatSelect
}) => {  const {
    chats: availableChats,
    loadingChats,
    fetchChats
  } = useChatData();

  const {
    activeConnections,
    connectToChat,
    disconnect<PERSON><PERSON><PERSON>hat,
    getConnectionState,
    messages,
    typingUsers,
    unreadCounts,
    markAsRead,
    maxConnections,
    setMaxConnections,
    autoConnectEnabled,
    setAutoConnectEnabled,
  } = useAdminWebSocket();  const [showSettings, setShowSettings] = useState(false);
  const [selectedPriorities, setSelectedPriorities] = useState<Set<string>>(new Set(['urgent', 'high']));

  // Load chats on mount
  useEffect(() => {
    if (availableChats.length === 0 && !loadingChats) {
      fetchChats({ status: 'pending' }); // Load active chats for admin dashboard
    }
  }, [availableChats.length, loadingChats, fetchChats]);

  // Auto-connect to high priority chats
  useEffect(() => {
    if (!autoConnectEnabled) return;

    const highPriorityChats = availableChats.filter(chat => 
      selectedPriorities.has(chat.priority) && 
      chat.status !== 'resolved' &&
      !activeConnections.has(chat.id.toString())
    );

    highPriorityChats.slice(0, maxConnections).forEach(chat => {
      connectToChat(chat.id.toString(), { priority: chat.priority, autoConnected: true });
    });
  }, [availableChats, autoConnectEnabled, selectedPriorities, maxConnections, activeConnections, connectToChat]);

  // Get connection statistics
  const getConnectionStats = () => {
    const connected = Array.from(activeConnections.values()).filter(conn => conn.state === 'connected').length;
    const connecting = Array.from(activeConnections.values()).filter(conn => conn.state === 'connecting').length;
    const total = activeConnections.size;
    
    return { connected, connecting, total };
  };

  const stats = getConnectionStats();

  // Priority colors
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-100 border-green-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  // Handle chat tile click
  const handleChatClick = (chat: SupportChat) => {
    const chatId = chat.id.toString();
    
    if (!activeConnections.has(chatId)) {
      connectToChat(chatId, { priority: chat.priority });
    }
    
    if (onChatSelect) {
      onChatSelect(chat);
    }
    
    markAsRead(chatId);
  };
  // Render active chat tiles
  const renderActiveChatTiles = () => {
    return Array.from(activeConnections.entries()).map(([chatId]) => {
      const chat = availableChats.find((c: SupportChat) => c.id.toString() === chatId);
      if (!chat) return null;

      const connectionState = getConnectionState(chatId);
      const unreadCount = unreadCounts.get(chatId) || 0;
      const chatMessages = messages.get(chatId) || [];
      const lastMessage = chatMessages[chatMessages.length - 1];
      const typing = typingUsers.get(chatId) || [];

      return (
        <motion.div
          key={chatId}
          layout
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          className="relative"
        >
          <Card 
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              unreadCount > 0 ? 'ring-2 ring-blue-200' : ''
            } ${getPriorityColor(chat.priority)} border-l-4`}
            onClick={() => handleChatClick(chat)}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-2">
                  <WebSocketIndicator connectionState={connectionState} />
                  <CardTitle className="text-sm font-medium truncate">
                    {chat.user_name}
                  </CardTitle>
                  {unreadCount > 0 && (
                    <Badge variant="destructive" className="text-xs">
                      {unreadCount}
                    </Badge>
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    disconnectFromChat(chatId);
                  }}
                  className="h-6 w-6 p-0"
                >
                  <X size={12} />
                </Button>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <p className="text-xs text-muted-foreground truncate mb-2">
                {chat.user_email}
              </p>
              
              {lastMessage && (
                <div className="text-xs">
                  <p className="truncate">
                    <span className={`font-medium ${
                      lastMessage.sender === 'user' ? 'text-blue-600' : 'text-green-600'
                    }`}>
                      {lastMessage.sender === 'user' ? 'Customer' : 'Support'}:
                    </span>
                    {' '}
                    {lastMessage.message}
                  </p>
                  <p className="text-muted-foreground mt-1">
                    {new Date(lastMessage.timestamp).toLocaleTimeString()}
                  </p>
                </div>
              )}
              
              {typing.length > 0 && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-xs text-blue-600 mt-2"
                >
                  <div className="flex items-center gap-1">
                    <div className="flex gap-1">
                      <div className="w-1 h-1 bg-blue-600 rounded-full animate-bounce" />
                      <div className="w-1 h-1 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-1 h-1 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                    <span>{typing[0].user_name} is typing...</span>
                  </div>
                </motion.div>
              )}
              
              <div className="flex justify-between items-center mt-2">
                <Badge variant="outline" className="text-xs">
                  {chat.priority}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {chat.status}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      );
    });
  };
  // Render available chats for connection
  const renderAvailableChats = () => {
    const unconnectedChats = availableChats.filter((chat: SupportChat) => 
      !activeConnections.has(chat.id.toString()) && 
      chat.status !== 'resolved'
    );

    if (unconnectedChats.length === 0) {
      return (
        <div className="text-center text-muted-foreground py-8">
          <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>All active chats are connected</p>
        </div>
      );
    }    return unconnectedChats.map((chat: SupportChat) => (
      <motion.div
        key={chat.id}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card 
          className={`cursor-pointer transition-all duration-200 hover:shadow-md opacity-60 hover:opacity-100 ${getPriorityColor(chat.priority)} border-l-4`}
          onClick={() => handleChatClick(chat)}
        >
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-sm font-medium truncate">
                {chat.user_name}
              </CardTitle>
              <Badge variant="outline" className="text-xs">
                {chat.priority}
              </Badge>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <p className="text-xs text-muted-foreground truncate mb-2">
              {chat.user_email}
            </p>
            <p className="text-xs truncate">
              {chat.message_snippet}
            </p>
            <div className="flex justify-between items-center mt-2">
              <span className="text-xs text-muted-foreground">
                {new Date(chat.updated_at).toLocaleTimeString()}
              </span>
              <Badge variant="outline" className="text-xs">
                {chat.status}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header with stats and controls */}
      <div className="bg-white rounded-lg border p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Admin Chat Dashboard</h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
          >
            <Settings size={16} className="mr-2" />
            Settings
          </Button>
        </div>
        
        <div className="grid grid-cols-4 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.connected}</div>
            <div className="text-xs text-muted-foreground">Connected</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.connecting}</div>
            <div className="text-xs text-muted-foreground">Connecting</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-xs text-muted-foreground">Total Active</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{maxConnections}</div>
            <div className="text-xs text-muted-foreground">Max Allowed</div>
          </div>
        </div>

        {/* Settings panel */}
        <AnimatePresence>
          {showSettings && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t pt-4"
            >
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Auto-connect enabled</label>
                  <div className="mt-1">
                    <Button
                      variant={autoConnectEnabled ? "default" : "outline"}
                      size="sm"
                      onClick={() => setAutoConnectEnabled(!autoConnectEnabled)}
                    >
                      {autoConnectEnabled ? 'Enabled' : 'Disabled'}
                    </Button>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium">Max connections</label>
                  <div className="mt-1">
                    <input
                      type="number"
                      min="1"
                      max="20"
                      value={maxConnections}
                      onChange={(e) => setMaxConnections(parseInt(e.target.value) || 8)}
                      className="w-20 px-2 py-1 text-sm border rounded"
                    />
                  </div>
                </div>
              </div>
              
              <div className="mt-4">
                <label className="text-sm font-medium">Auto-connect priorities</label>
                <div className="mt-2 flex gap-2">
                  {['urgent', 'high', 'medium', 'low'].map(priority => (
                    <Button
                      key={priority}
                      variant={selectedPriorities.has(priority) ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        const newPriorities = new Set(selectedPriorities);
                        if (newPriorities.has(priority)) {
                          newPriorities.delete(priority);
                        } else {
                          newPriorities.add(priority);
                        }
                        setSelectedPriorities(newPriorities);
                      }}
                      className={`capitalize ${getPriorityColor(priority)}`}
                    >
                      {priority}
                    </Button>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Active connections */}
      {activeConnections.size > 0 && (
        <div>
          <h3 className="text-md font-medium mb-4">Active Connections</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <AnimatePresence>
              {renderActiveChatTiles()}
            </AnimatePresence>
          </div>
        </div>
      )}

      {/* Available chats */}
      <div>
        <h3 className="text-md font-medium mb-4">Available Chats</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {renderAvailableChats()}
        </div>
      </div>
    </div>
  );
};
