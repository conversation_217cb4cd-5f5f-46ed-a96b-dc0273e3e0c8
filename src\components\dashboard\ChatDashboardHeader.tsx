'use client';

import React, { useContext} from 'react';
import { AuthContext } from '@/components/context/AuthContext';
import { MenuIcon, HeadphonesIcon } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

const ChatDashboardHeader = () => {
  const { isSidepanelOpen, setIsSidepanelOpen, user } = useContext(AuthContext);
  
  return (
    <header className="h-16 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800 fixed top-0 left-0 right-0 z-30 flex items-center justify-between px-4 md:px-6">
      <div className="flex items-center gap-3">
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full hover:bg-slate-100 dark:hover:bg-gray-800"
          onClick={() => setIsSidepanelOpen(!isSidepanelOpen)}
          aria-label={isSidepanelOpen ? "Chiudi sidebar" : "Apri sidebar"}
        >
          <MenuIcon className="h-5 w-5 text-[#113158]" />
        </Button>

        <Link href="/dashboard" className="flex items-center gap-2">
          <HeadphonesIcon className="h-5 w-5 text-[#febd49]" />
          <h1 className="text-lg font-bold text-[#113158] dark:text-white">Portale Supporto</h1>
        </Link>
      </div>

      <div className="flex items-center gap-2">
        {user && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600 dark:text-gray-300 hidden md:inline">
              {user.name || user.email}
            </span>
            <Avatar className="h-8 w-8 border border-gray-200 dark:border-gray-700">
              <AvatarFallback className="bg-[#113158] text-white text-xs">
                {user.name ? user.name[0].toUpperCase() : user.email ? user.email[0].toUpperCase() : 'U'}
              </AvatarFallback>
            </Avatar>
          </div>
        )}
      </div>
    </header>
  );
};

export default ChatDashboardHeader;