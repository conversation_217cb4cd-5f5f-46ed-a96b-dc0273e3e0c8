import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  transpilePackages: ["tw-animate-css"],
  turbopack: {
    // Ensures Turbopack properly handles CSS files
    resolveExtensions: ['.tsx', '.ts', '.jsx', '.js', '.css', '.json'],
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'heibookybucket.fra1.digitaloceanspaces.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

export default nextConfig;
