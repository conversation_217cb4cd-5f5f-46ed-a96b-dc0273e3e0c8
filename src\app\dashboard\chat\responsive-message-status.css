/* Responsive message status animations and styling */

/* Base responsive styles */
@media (max-width: 768px) {
  .status-icon-container {
    width: 14px;
    height: 14px;
  }
  
  .status-tooltip {
    font-size: 0.6rem;
    padding: 3px 6px;
  }
}

@media (max-width: 480px) {
  .message-timestamp {
    font-size: 0.65rem;
  }
  
  .message-bubble-user,
  .message-bubble-agent {
    padding: 10px 14px;
    border-radius: 16px 16px 16px 4px;
  }
  
  .message-bubble-agent {
    border-radius: 16px 16px 4px 16px;
  }
}

/* Enhanced hover effects for touch devices */
@media (hover: none) {
  .status-icon-container:active .status-tooltip {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.5s;
  }
  
  .message-container:active .status-text {
    opacity: 1;
  }
  
  /* Make status icons always visible on mobile */
  .message-timestamp {
    opacity: 0.75;
  }
}

/* RTL language support */
[dir="rtl"] .status-icon-container {
  margin-right: 4px;
  margin-left: 0;
}

[dir="rtl"] .status-tooltip {
  right: auto;
  left: 0;
}

/* Dark mode specific responsive styles */
@media (prefers-color-scheme: dark) {
  .status-icon-sending {
    color: rgba(224, 224, 224, 0.5);
  }
  
  .status-icon-sent,
  .status-icon-delivered {
    color: #e0e0e0;
  }
  
  .status-icon-read {
    color: #34d399;
  }
}

/* High contrast mode support for accessibility */
@media (forced-colors: active) {
  .status-icon-container {
    forced-color-adjust: none;
  }
  
  .status-icon-sending,
  .status-icon-sent,
  .status-icon-delivered {
    color: CanvasText;
  }
  
  .status-icon-read {
    color: Highlight;
  }
}

/* Print mode styling */
@media print {
  .status-icon-container,
  .status-tooltip {
    display: none !important;
  }
}
