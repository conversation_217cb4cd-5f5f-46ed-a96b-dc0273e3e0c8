'use client';

import { useContext, useEffect, useState, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { AuthContext } from '@/components/context/AuthContext';
import Loader1 from '@/components/loaders/Loader1';

interface ProtectedRouteProps {
  children: React.ReactNode;
  roles?: string[]; // Optional roles for role-based access control
  requiresAdmin?: boolean; // Flag to indicate if route requires admin privileges
  requiresStaff?: boolean; // Flag to indicate if route requires staff privileges
}

/**
 * Component to protect routes that require authentication
 * Redirects to login if user is not authenticated
 * Redirects to unauthorized page if user lacks necessary permissions
 */
const ProtectedRoute = ({ 
  children, 
  roles, 
  requiresAdmin = false, 
  requiresStaff = false 
}: ProtectedRouteProps) => {
  const { isAuthenticated, isLoading, checkAuth } = useContext(AuthContext);
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);
  const [hasPermission, setHasPermission] = useState(true);
  const authCheckInProgressRef = useRef(false);

  useEffect(() => {
    // Prevent duplicate auth checks
    if (authCheckInProgressRef.current) {
      return;
    }

    const verifyAuth = async () => {
      authCheckInProgressRef.current = true;
      
      try {
        // If already authenticated and not loading, skip auth check
        if (isAuthenticated && !isLoading) {
          setHasPermission(true);
          setIsChecking(false);
          return;
        }

        // Check authentication status directly
        // This will internally handle token refresh if needed
        const isValid = await checkAuth();

        if (!isValid) {
          // Redirect to login with return path
          const redirectPath = encodeURIComponent(pathname);
          router.push(`/auth?redirect=${redirectPath}&error=session_expired`);
          return;
        }

        // Here you would check user roles/permissions
        // This is a placeholder for the permission check
        // In a real application, you'd check against user roles stored in context or fetched from API
        const userHasPermission = true; // Replace with actual permission check
        
        if (requiresAdmin || requiresStaff || roles?.length) {
          // If permissions are required but user doesn't have them
          if (!userHasPermission) {
            setHasPermission(false);
            router.push('/unauthorized');
            return;
          }
        }
        
        setHasPermission(true);
      } catch (error) {
        console.error('Authentication verification failed:', error);
        // Redirect to login on error
        const redirectPath = encodeURIComponent(pathname);
        router.push(`/auth?redirect=${redirectPath}&error=authentication_error`);
      } finally {
        setIsChecking(false);
        authCheckInProgressRef.current = false;
      }
    };

    verifyAuth();
  }, [pathname, isAuthenticated, isLoading, checkAuth, requiresAdmin, requiresStaff, roles, router]); // Added all required dependencies
  // Show loading state while checking authentication
  if (isLoading || isChecking) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <Loader1 />
      </div>
    );
  }

  // If authenticated and has permission, render children
  return (isAuthenticated && hasPermission) ? <>{children}</> : null;
};

export default ProtectedRoute;
