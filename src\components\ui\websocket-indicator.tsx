'use client';

import React from 'react';
import { Wifi, WifiOff, Loader2 } from 'lucide-react';
import { WebSocketConnectionState } from '@/services/websocketService';
import { Tooltip } from '@/components/ui/tooltip';
import '@/app/dashboard/chat/chat-styles.css';

interface WebSocketIndicatorProps {
  connectionState: WebSocketConnectionState;
}

export const WebSocketIndicator = ({ connectionState }: WebSocketIndicatorProps) => {
  let icon = null;
  let label = '';
  let tooltipText = '';
  let className = 'ws-connection-indicator flex items-center gap-1 text-xs';
  
  switch (connectionState) {
    case 'connected':
      icon = <Wifi size={12} className="animate-pulse-slow" />;
      label = 'Live';
      tooltipText = 'Connected to real-time chat updates';
      className += ' ws-connection-indicator-connected';
      break;
    case 'connecting':
      icon = <Loader2 size={12} className="animate-spin" />;
      label = 'Connecting';
      tooltipText = 'Establishing connection...';
      className += ' ws-connection-indicator-connecting';
      break;
    case 'reconnecting':
      icon = <Loader2 size={12} className="animate-spin" />;
      label = 'Reconnecting';
      tooltipText = 'Attempting to reconnect...';
      className += ' ws-connection-indicator-reconnecting';
      break;
    case 'error':
      icon = <WifiOff size={12} className="text-red-500" />;
      label = 'Error';
      tooltipText = 'Connection error. Check authentication.';
      className += ' ws-connection-indicator-error text-red-500';
      break;
    case 'disconnected':
    default:
      icon = <WifiOff size={12} />;
      label = 'Offline';
      tooltipText = 'No real-time updates. Try refreshing.';
      className += ' ws-connection-indicator-disconnected';
      break;
  }

  return (
    <Tooltip content={tooltipText}>
      <div className={className}>
        {icon}
        <span className="hidden sm:inline">{label}</span>
      </div>
    </Tooltip>
  );
};
