'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { Home, ArrowLeft, Search } from 'lucide-react'
import Button from '@/components/buttons/Button'
import styles from './not-found.module.css'

export default function NotFound() {
  const router = useRouter()

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {/* Error Code */}
        <div className={styles.errorCode}>404</div>

        {/* Illustration */}
        <div className={styles.illustration}>
          <svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
            {/* Background Circle */}
            <circle cx="100" cy="100" r="90" fill="rgba(19, 49, 87, 0.05)" />
            <circle cx="100" cy="100" r="75" stroke="var(--blue)" strokeWidth="2" strokeDasharray="5,5" opacity="0.3" />
            
            {/* Main Character Circle */}
            <circle cx="100" cy="100" r="60" fill="white" stroke="var(--blue)" strokeWidth="3" />
            
            {/* Eyes */}
            <circle cx="85" cy="85" r="8" fill="var(--blue)" />
            <circle cx="115" cy="85" r="8" fill="var(--blue)" />
            <circle cx="87" cy="83" r="3" fill="white" />
            <circle cx="117" cy="83" r="3" fill="white" />
            
            {/* Mouth - Sad */}
            <path d="M 75 120 Q 100 110 125 120" stroke="var(--blue)" strokeWidth="3" fill="none" strokeLinecap="round" />
            
            {/* Search Icon */}
            <circle cx="140" cy="60" r="12" stroke="var(--accent)" strokeWidth="3" fill="none" />
            <path d="m148 68 4 4" stroke="var(--accent)" strokeWidth="3" strokeLinecap="round" />
            
            {/* Question Marks */}
            <text x="60" y="45" fontSize="24" fill="var(--accent)" fontWeight="bold">?</text>
            <text x="145" y="140" fontSize="20" fill="var(--accent)" fontWeight="bold">?</text>
            <text x="35" y="120" fontSize="16" fill="var(--accent)" fontWeight="bold">?</text>
            
            {/* Floating Elements */}
            <circle cx="50" cy="70" r="2" fill="var(--accent)" opacity="0.6" />
            <circle cx="160" cy="110" r="2" fill="var(--accent)" opacity="0.6" />
            <circle cx="45" cy="150" r="1.5" fill="var(--blue)" opacity="0.4" />
            <circle cx="155" cy="45" r="1.5" fill="var(--blue)" opacity="0.4" />
          </svg>
        </div>

        {/* Title */}
        <h1 className={styles.title}>Pagina Non Trovata</h1>
        
        {/* Description */}
        <p className={styles.description}>
          La pagina che stai cercando non esiste o è stata spostata. Verifica l&apos;URL o esplora le sezioni disponibili del sito.
        </p>

        {/* Suggestions */}
        <div className={styles.suggestions}>
          <div className={styles.suggestionItem}>
            <Search className="h-4 w-4 text-gray-500" />
            <span>Controlla l&apos;ortografia dell&apos;URL</span>
          </div>
          <div className={styles.suggestionItem}>
            <Home className="h-4 w-4 text-gray-500" />
            <span>Torna alla dashboard principale</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className={styles.buttonContainer}>
          <div className={styles.primaryButton}>
            <Button
              text="Torna alla Dashboard"
              color="white"
              backgroundColor="var(--blue)"
              icon={<Home width={16} />}
              fontSize="14px"
              onClick={() => router.push('/dashboard')}
            />
          </div>
          
          <button 
            onClick={() => router.back()}
            className={styles.secondaryButton}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Torna Indietro
          </button>
        </div>
      </div>
    </div>
  )
}