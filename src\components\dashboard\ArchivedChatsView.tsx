'use client';

import React, { useEffect, useState } from 'react';
import { useChatData } from '@/components/context/ChatDataContext';
import Loader1 from '@/components/loaders/Loader1';
import ChatCard from '@/components/dashboard/ChatCard';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { RefreshCw, Archive, Search, AlertTriangle } from 'lucide-react';
import { SupportChat } from './SupportChatList';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';

export const ArchivedChatsView = ({ onChatSelect }: { onChatSelect?: (chat: SupportChat) => void }) => {
  const router = useRouter();
  const { 
    chats, 
    loadingChats, 
    chatsError, 
    fetchChats
  } = useChatData();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'latest' | 'oldest'>('latest');
  const [timeFilter, setTimeFilter] = useState<'all' | 'week' | 'month' | 'quarter'>('all');

  // Get archived (resolved) chats from context
  const archivedChats = chats.filter(chat => chat.status === 'resolved');

  useEffect(() => {
    const fetchArchivedChats = async () => {
      await fetchChats({ status: 'resolved' });
    };

    // Fetch archived chats on mount if we don't have any resolved chats yet
    if (archivedChats.length === 0 && !loadingChats) {
      fetchArchivedChats();
    }
  }, [archivedChats.length, loadingChats, fetchChats]);

  // Separate function for manual refresh
  const handleRefreshChats = async () => {
    await fetchChats({ status: 'resolved' });
  };

  // Filter and sort the chats based on user selections
  const filteredChats = archivedChats.filter(chat => {
    // Filter by search term (name, email, or message content)
    if (searchTerm && !chat.user_name.toLowerCase().includes(searchTerm.toLowerCase()) && 
        !chat.user_email.toLowerCase().includes(searchTerm.toLowerCase()) && 
        !chat.message_snippet.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    
    // Filter by time
    if (timeFilter !== 'all') {
      const chatDate = new Date(chat.updated_at);
      const now = new Date();
      
      switch(timeFilter) {
        case 'week':
          const oneWeekAgo = new Date();
          oneWeekAgo.setDate(now.getDate() - 7);
          if (chatDate < oneWeekAgo) return false;
          break;
        case 'month':
          const oneMonthAgo = new Date();
          oneMonthAgo.setMonth(now.getMonth() - 1);
          if (chatDate < oneMonthAgo) return false;
          break;
        case 'quarter':
          const oneQuarterAgo = new Date();
          oneQuarterAgo.setMonth(now.getMonth() - 3);
          if (chatDate < oneQuarterAgo) return false;
          break;
      }
    }
    
    return true;
  }).sort((a, b) => {
    // Sort by date
    if (sortBy === 'latest') {
      return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
    } else {
      return new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime();
    }
  });

  return (
    <div className="flex flex-col space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 bg-gradient-to-r from-blue-500/10 to-yellow-500/10 p-4 rounded-lg shadow-sm border">
        <div>
          <h2 className="text-xl lg:text-2xl font-semibold text-blue-700 dark:text-blue-400">
            <Archive className="inline-block mr-2 h-6 w-6" />
            Conversazioni Archiviate
          </h2>          <p className="text-muted-foreground mt-1">
            {loadingChats ? 'Caricamento...' : `${filteredChats.length} ${filteredChats.length === 1 ? 'chat visualizzata' : 'chat visualizzate'}`}
          </p>
        </div>
        <Button
          onClick={handleRefreshChats}
          variant="outline"
          disabled={loadingChats}
          className="h-9 gap-2 border-blue-200 hover:bg-blue-50 hover:text-blue-700 dark:border-blue-800 dark:hover:bg-blue-900/20 dark:hover:text-blue-400"
        >
          {loadingChats ? 
            <div className="w-4 h-4 border-2 border-t-transparent border-current rounded-full animate-spin"></div> : 
            <RefreshCw className="h-4 w-4" />
          }
          {loadingChats ? 'Caricamento...' : 'Aggiorna'}
        </Button>
      </div>

      {/* Search and filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="flex items-center bg-white dark:bg-gray-900 rounded-md border border-input overflow-hidden focus-within:ring-1 focus-within:ring-blue-500 focus-within:border-blue-500">
          <div className="flex items-center justify-center pl-3 pr-1">
            <Search size={16} className="text-muted-foreground" />
          </div>
          <Input
            placeholder="Cerca per nome, email o contenuto..."
            className="pl-2 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div>
          <Select 
            value={timeFilter} 
            onChange={(e) => setTimeFilter(e.target.value as 'all' | 'week' | 'month' | 'quarter')}
            className="bg-white dark:bg-gray-900 w-full"
          >
            <option value="all">Da sempre</option>
            <option value="week">Ultima settimana</option>
            <option value="month">Ultimo mese</option>
            <option value="quarter">Ultimo trimestre</option>
          </Select>
        </div>
        <div>
          <Select 
            value={sortBy} 
            onChange={(e) => setSortBy(e.target.value as 'latest' | 'oldest')}
            className="bg-white dark:bg-gray-900 w-full"
          >
            <option value="latest">Più recenti prima</option>
            <option value="oldest">Meno recenti prima</option>
          </Select>
        </div>
      </div>      {/* Content */}
      {loadingChats ? (
        <div className="flex justify-center items-center py-16 bg-card/50 rounded-lg border border-dashed">
          <div className="flex flex-col items-center">
            <Loader1 />
            <p className="mt-4 text-muted-foreground">Caricamento chat archiviate...</p>
          </div>
        </div>
      ) : chatsError ? (
        <div className="bg-destructive/10 border border-destructive/30 p-6 my-4 text-destructive rounded-lg flex flex-col items-center justify-center">
          <AlertTriangle className="h-8 w-8 mb-2" />
          <p>{chatsError}</p>
          <Button 
            variant="outline"
            className="mt-3 border-destructive/50 hover:bg-destructive/10" 
            onClick={handleRefreshChats}
          >
            Riprova
          </Button>
        </div>
      ) : filteredChats.length === 0 ? (
        <div className="bg-gradient-to-b from-blue-50/50 to-yellow-50/50 dark:from-blue-900/10 dark:to-yellow-900/10 rounded-lg p-12 text-center shadow-sm border flex flex-col items-center">
          <Archive className="h-12 w-12 text-blue-400/70 mb-3" />
          <p className="text-blue-700 dark:text-blue-400 text-lg font-medium">Nessuna conversazione archiviata trovata</p>
          <p className="text-muted-foreground mt-1">Quando le chat vengono contrassegnate come risolte, appariranno qui.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredChats.map(chat => (
            <ChatCard 
              key={chat.id} 
              chat={chat} 
              onClick={() => {
                if (onChatSelect) {
                  onChatSelect(chat);
                } else {
                  router.push(`/dashboard/chat/${chat.id}`);
                }
              }}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ArchivedChatsView;
