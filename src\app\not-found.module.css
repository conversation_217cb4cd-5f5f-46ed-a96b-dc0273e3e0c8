.container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, var(--background) 0%, #f8f9fa 100%);
  padding: 1rem;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(19, 49, 87, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(252, 181, 31, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(19, 49, 87, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

.content {
  max-width: 520px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background-color: white;
  border-radius: 20px;
  padding: 3rem 2rem;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.errorCode {
  font-size: 4.5rem;
  font-weight: 900;
  color: var(--blue);
  line-height: 1;
  margin-bottom: 1rem;
  position: relative;
  text-shadow: 0 4px 8px rgba(19, 49, 87, 0.1);
  background: linear-gradient(135deg, var(--blue) 0%, #1e4d72 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.errorCode::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, var(--accent), #ffd700);
  border-radius: 2px;
  box-shadow: 0 2px 4px rgba(252, 181, 31, 0.3);
}

.illustration {
  margin: 1.5rem 0 2rem 0;
  animation: float 6s ease-in-out infinite;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-8px) rotate(1deg);
  }
  75% {
    transform: translateY(-4px) rotate(-1deg);
  }
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--blue);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.description {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
  max-width: 90%;
}

.suggestions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
  width: 100%;
  max-width: 300px;
}

.suggestionItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  font-size: 0.875rem;
  color: #4b5563;
  transition: all 0.2s ease-in-out;
}

.suggestionItem:hover {
  background-color: #f3f4f6;
  border-color: var(--accent);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.buttonContainer {
  width: 100%;
  max-width: 280px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.primaryButton {
  width: 100%;
}

.secondaryButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  color: var(--blue);
  background-color: transparent;
  border: 2px solid var(--blue);
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  width: 100%;
}

.secondaryButton:hover {
  background-color: var(--blue);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(19, 49, 87, 0.15);
}

.secondaryButton:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 640px) {
  .content {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }
  
  .errorCode {
    font-size: 3.5rem;
  }
  
  .title {
    font-size: 1.75rem;
  }
  
  .description {
    font-size: 0.9rem;
  }
  
  .illustration {
    margin: 1rem 0 1.5rem 0;
  }
  
  .suggestions {
    max-width: 100%;
  }
  
  .buttonContainer {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }
  
  .content {
    padding: 1.5rem 1rem;
  }
  
  .errorCode {
    font-size: 3rem;
  }
  
  .title {
    font-size: 1.5rem;
  }
  
  .illustration svg {
    width: 160px;
    height: 160px;
  }
}