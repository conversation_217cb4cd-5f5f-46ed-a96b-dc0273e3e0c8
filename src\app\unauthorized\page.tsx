'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { ShieldX, ArrowLeft, LogIn } from 'lucide-react';
import AuthLogo from '@/components/auth/AuthLogo';
import Button from '@/components/buttons/Button';
import { useAuth } from '@/components/context/AuthContext';
import styles from './unauthorized.module.css';

const UnauthorizedPage = () => {
  const router = useRouter();
  const { logout } = useAuth();

  // <PERSON>le returning to auth page by clearing tokens first
  const handleReturnToAuth = async () => {
    try {
      // Clear all tokens and authentication state
      await logout();
      // Navigation to auth page is handled by the logout function
    } catch (error) {
      console.error('Error during logout:', error);
      // If logout fails, still navigate to auth page
      router.push('/auth');
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {/* Logo Section */}
        <div className={styles.logoSection}>
          <AuthLogo />
        </div>
        
        {/* Error Icon */}
        <div className={styles.errorIcon}>
          <ShieldX className="h-16 w-16 text-red-500" />
        </div>

        {/* Error Code */}
        <div className={styles.errorCode}>401</div>
        
        {/* Title */}
        <h1 className={styles.title}>Accesso Non Autorizzato</h1>
        
        {/* Alert Box */}
        <div className={styles.alertBox}>
          <div className={styles.alertContent}>
            <div className={styles.alertIconWrapper}>
              <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className={styles.alertMessage}>
              <p className={styles.alertText}>
                Non disponi delle autorizzazioni necessarie per accedere a questa area.
              </p>
            </div>
          </div>
        </div>
        
        {/* Description */}
        <p className={styles.description}>
          Se ritieni che questo sia un errore, contatta l&apos;amministratore del sistema o accedi con un account che dispone delle autorizzazioni appropriate.
        </p>
          {/* Action Buttons */}
        <div className={styles.buttonContainer}>
          <div className={styles.primaryButton}>
            <Button
              onClick={handleReturnToAuth}
              text="Torna alla pagina di accesso"
              backgroundColor="var(--blue)"
              color="white"
              fontSize="14px"
              border="none"
              icon={<LogIn width={16} />}
            />
          </div>
          
          <button 
            onClick={() => router.back()}
            className={styles.secondaryButton}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Torna alla pagina precedente
          </button>
        </div>
      </div>
    </div>
  );
};

export default UnauthorizedPage;