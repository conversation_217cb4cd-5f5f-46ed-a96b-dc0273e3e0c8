﻿// Token management is now handled by tokenService.ts
import {
  getAccessToken,
  setAccessToken,
  getRefreshToken,
  setRefreshToken,
  clearTokens,
} from "./tokenService";

// Import the apiClient for making API requests
import { apiClient } from "./apiClient";

// Import the SupportChat interface
import { SupportChat } from "@/components/dashboard/SupportChatList";

// For backward compatibility
export const setToken = (token: string, refreshToken?: string) => {
  setAccessToken(token);
  if (refreshToken) {
    setRefreshToken(refreshToken);
  }
}
export const getToken = () => {
  return getAccessToken();
}
export const deleteToken = () => {
  clearTokens();
}

//Token API
export const verifyToken = async () => {
  const token = getToken();

  if (!token) {
    return {
      status: 401
    };
  }

  try {
    const response = await apiClient.post("/users/verify-token/", { token });
    
    // Check for network error first
    if (response.status === 0) {
      return {
        status: 500
      };
    }

    return {
      status: response.status
    };
  } catch (error) {
    console.error("Token verification error:", error);
    
    // Handle specific network errors
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      return {
        status: 500
      };
    }
    
    return {
      status: 500
    };
  }
}
export const refreshToken = async (token: string) => {
  try {
    const response = await apiClient.post("/users/refresh-token/", { refresh: token });
    
    // Check if the response is ok before trying to parse JSON
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Token refresh failed with status ${response.status}:`, errorText);
      throw new Error(`HTTP ${response.status}: ${errorText || 'Token refresh failed'}`);
    }
    
    const data = await response.json();

    // If the response contains new tokens, update them
    if (data.access && data.refresh) {
      setToken(data.access, data.refresh);
    }

    return data;
  } catch (error) {
    console.error("Token refresh error:", error);
    
    // Provide more specific error information for different failure types
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      // Network error - server might be down or no internet connection
      throw new Error('Network error: Unable to connect to authentication server. Please check your internet connection.');
    }
    
    if (error instanceof Error) {
      throw error; // Re-throw the original error with context
    }
    
    // Generic fallback error
    throw new Error('Token refresh failed due to an unexpected error');
  }
}

//Account (Auth)
/**
 * Login a user
 * @param email User email
 * @param password User password
 * @param _isAccessingAdminRoute Parameter maintained for API compatibility but not used in request
 */
export const login = async (email: string, password: string) => {
  try {
    // _isAccessingAdminRoute is intentionally not used in the implementation
    // but kept for compatibility with the function signature
    const response = await apiClient.post("/users/staff/login/", { email, password });
    
    // Check for network error first
    if (response.status === 0) {
      return {
        data: { error: "Network error: Unable to connect to server. Please check your internet connection." },
        status: 500
      };
    }
    
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error("Failed to parse login response as JSON:", jsonError);
      return {
        data: { error: "Invalid response from server" },
        status: response.status || 500
      };
    }

    // If login is successful and we have tokens, store them
    if (response.status === 200 && data.access) {
      setToken(data.access, data.refresh);
    }

    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error("Login error:", error);
    
    // Handle specific network errors
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      return {
        data: { error: "Network error: Unable to connect to server. Please check your internet connection." },
        status: 500
      };
    }
    
    return {
      data: { error: "Network error occurred" },
      status: 500
    };
  }
};

export const logout = async () => {
  const refreshTokenValue = getRefreshToken();

  try {
    const response = await apiClient.post("/users/logout/", {
      refresh: refreshTokenValue || getToken()
    });

    // Clear tokens regardless of the response
    clearTokens();

    const data = await response.json();
    return {
      data,
      status: response.status
    };
  } catch (error) {
    // Clear tokens even if the API call fails
    clearTokens();
    console.error("Logout error:", error);
    return {
      data: { message: "Logged out" },
      status: 200
    };
  }
}

export const viewProfile = async () => {
  try {
    const response = await apiClient.get('/users/profile');
    
    // Check for network error first
    if (response.status === 0) {
      return {
        data: { error: "Network error: Unable to connect to server" },
        status: 500
      };
    }
    
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error("Failed to parse profile response as JSON:", jsonError);
      return {
        data: { error: "Invalid response from server" },
        status: response.status || 500
      };
    }
    
    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error('Error viewing profile:', error);
    
    // Handle specific network errors
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      return {
        data: { error: "Network error: Unable to connect to server" },
        status: 500
      };
    }
    
    return {
      data: { error: "Failed to load profile" },
      status: 500
    };
  }
}

// Support Chat API
export const getSupportChats = async (filters?: {
  status?: "pending" | "in_progress" | "resolved";
  priority?: "low" | "medium" | "high" | "urgent";
  assigned_agent?: string;
  sort_by?: "latest" | "priority";
}) => {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (filters?.status) queryParams.append("status", filters.status);
    if (filters?.priority) queryParams.append("priority", filters.priority);
    if (filters?.assigned_agent) queryParams.append("assigned_agent", filters.assigned_agent);
    if (filters?.sort_by) queryParams.append("sort_by", filters.sort_by);
    
    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : "";
    
    const response = await apiClient.get(`/support/chats${queryString}`);
    
    // Check for network error first
    if (response.status === 0) {
      return {
        data: { error: "Network error: Unable to connect to server" },
        status: 500
      };
    }
    
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error("Failed to parse chats response as JSON:", jsonError);
      return {
        data: { error: "Invalid response from server" },
        status: response.status || 500
      };
    }
    
    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error("Error fetching support chats:", error);
    
    // Handle specific network errors
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      return {
        data: { error: "Network error: Unable to connect to server" },
        status: 500
      };
    }
    
    return {
      data: { error: "Failed to load support chats" },
      status: 500
    };
  }
}

/**
 * Get a specific chat details by ID
 * GET /support/chats/{id}
 */
export const fetchChatById = async (id: string): Promise<SupportChat | null> => {
  try {
    const response = await apiClient.get(`/support/chats/${id}`);
    
    // Check for network error first
    if (response.status === 0) {
      console.error("Network error fetching chat by ID:", id);
      return null;
    }
    
    if (!response.ok) {
      throw new Error(`Failed to fetch chat with ID ${id}`);
    }
    
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error("Failed to parse chat response as JSON:", jsonError);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error("Error fetching chat by ID:", error);
    
    // Handle specific network errors gracefully
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      console.error("Network connectivity issue when fetching chat by ID:", id);
    }
    
    return null;
  }
};

/**
 * Get chat messages by chat ID
 * GET /support/messages?chat_id={id}
 * @returns Messages with pagination data
 */
export const getChatMessages = async (id: string) => {
  try {
    const response = await apiClient.get(`/support/messages?chat_id=${id}`);
    
    // Check for network error first
    if (response.status === 0) {
      return {
        data: { results: [] },
        status: 500
      };
    }
    
    if (!response.ok) {
      throw new Error(`Failed to fetch messages for chat ID ${id}`);
    }
    
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error("Failed to parse messages response as JSON:", jsonError);
      return {
        data: { results: [] },
        status: response.status || 500
      };
    }
    
    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error("Error fetching chat messages:", error);
    
    // Handle specific network errors
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      console.error("Network connectivity issue when fetching messages for chat:", id);
    }
    
    return {
      data: { results: [] },
      status: 500
    };
  }
};

export const sendChatMessage = async (chat_id: string, message: string, attachments?: File[]) => {
  try {
    const formData = new FormData();
    formData.append("message", message);
    formData.append("chat_id", chat_id);
    
    if (attachments && attachments.length > 0) {
      attachments.forEach((file, index) => {
        formData.append(`attachment_${index}`, file);
      });
    }
    
    const response = await apiClient.post("/support/messages/", formData, {
      headers: {
        // Do not set Content-Type with FormData, browser will set it with boundary
      }
    });
    
    // Check for network error first
    if (response.status === 0) {
      return {
        data: { error: "Network error: Unable to send message" },
        status: 500
      };
    }
    
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error("Failed to parse send message response as JSON:", jsonError);
      return {
        data: { error: "Invalid response from server" },
        status: response.status || 500
      };
    }
    
    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error("Error sending chat message:", error);
    
    // Handle specific network errors
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      return {
        data: { error: "Network error: Unable to send message" },
        status: 500
      };
    }
    
    return {
      data: { error: "Failed to send message" },
      status: 500
    };
  }
}

export const updateChatStatus = async (chatId: number, status: "pending" | "in_progress" | "resolved") => {
  try {
    const response = await apiClient.patch(`/support/chats/${chatId}/`, {
      status
    });
    
    const data = await response.json();
    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error("Error updating chat status:", error);
    return {
      data: { error: "Failed to update chat status" },
      status: 500
    };
  }
}

export const updateChatPriority = async (chatId: number, priority: "low" | "medium" | "high" | "urgent") => {
  try {
    const response = await apiClient.patch(`/support/chats/${chatId}/`, {
      priority
    });
    
    const data = await response.json();
    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error("Error updating chat priority:", error);
    return {
      data: { error: "Failed to update chat priority" },
      status: 500
    };
  }
}

/**
 * Get high priority chats for auto-connection
 * GET /support/chats?priority=high&priority=urgent&status=pending&status=in_progress
 * @returns High priority chats that should be auto-connected
 */
export const getHighPriorityChats = async () => {
  try {
    const queryParams = new URLSearchParams();
    // Get both high and urgent priority chats
    queryParams.append("priority", "high");
    queryParams.append("priority", "urgent");
    // Only get active chats (not resolved)
    queryParams.append("status", "pending");
    queryParams.append("status", "in_progress");
    // Sort by priority and latest activity
    queryParams.append("sort_by", "priority");
    
    const response = await apiClient.get(`/support/chats?${queryParams.toString()}`);
    
    // Check for network error first
    if (response.status === 0) {
      return {
        data: { results: [] },
        status: 500
      };
    }
    
    if (!response.ok) {
      throw new Error('Failed to fetch high priority chats');
    }
    
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error("Failed to parse high priority chats response as JSON:", jsonError);
      return {
        data: { results: [] },
        status: response.status || 500
      };
    }
    
    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error("Error fetching high priority chats:", error);
    
    // Handle specific network errors
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      return {
        data: { results: [] },
        status: 500
      };
    }
    
    return {
      data: { results: [] },
      status: 500
    };
  }
}
