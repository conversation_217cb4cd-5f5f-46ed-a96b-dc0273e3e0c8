'use client';

import { ReactNode } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { motion } from 'framer-motion';

interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: ReactNode;
  trendValue?: number;
  trendDirection?: 'up' | 'down' | 'neutral';
  color?: 'blue' | 'yellow' | 'green' | 'red';
}

const StatsCard = ({
  title,
  value,
  description,
  icon,
  trendValue,
  trendDirection = 'neutral',
  color = 'blue',
}: StatsCardProps) => {
  const colorClasses = {
    blue: {
      bg: 'bg-[#113158]/10',
      iconBg: 'bg-[#113158]',
      text: 'text-[#113158]',
      border: 'border-[#113158]/20',
      hover: 'hover:bg-[#113158]/15',
      iconShadow: 'shadow-[#113158]/20',
      trendUp: 'text-[#113158] bg-[#113158]/10',
      trendDown: 'text-red-600 bg-red-50'
    },
    yellow: {
      bg: 'bg-[#febd49]/10',
      iconBg: 'bg-[#febd49]',
      text: 'text-[#113158]',
      border: 'border-[#febd49]/20',
      hover: 'hover:bg-[#febd49]/15',
      iconShadow: 'shadow-[#febd49]/20',
      trendUp: 'text-green-600 bg-green-50',
      trendDown: 'text-red-600 bg-red-50'
    },
    green: {
      bg: 'bg-green-100',
      iconBg: 'bg-green-500',
      text: 'text-green-700',
      border: 'border-green-200',
      hover: 'hover:bg-green-200/50',
      iconShadow: 'shadow-green-500/20',
      trendUp: 'text-green-600 bg-green-50',
      trendDown: 'text-red-600 bg-red-50'
    },
    red: {
      bg: 'bg-red-100',
      iconBg: 'bg-red-500',
      text: 'text-red-700',
      border: 'border-red-200',
      hover: 'hover:bg-red-200/50',
      iconShadow: 'shadow-red-500/20',
      trendUp: 'text-green-600 bg-green-50',
      trendDown: 'text-red-600 bg-red-50'
    },
  };
  const trendColorClass = 
    trendDirection === 'up' 
      ? `${colorClasses[color].trendUp} font-medium` 
      : trendDirection === 'down' 
        ? `${colorClasses[color].trendDown} font-medium` 
        : 'text-gray-500';

  const trendIcon = 
    trendDirection === 'up' 
      ? '↑' 
      : trendDirection === 'down' 
        ? '↓' 
        : '→';  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ 
        y: -4, 
        boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
        transition: { duration: 0.2 } 
      }}
      className="min-h-[130px]"
    >
      <Card 
        className={`border ${colorClasses[color].border} shadow-md hover:shadow-lg overflow-hidden rounded-lg ${colorClasses[color].bg} ${colorClasses[color].hover} transition-all duration-300 cursor-pointer h-full`}
      >
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 pt-5 px-6">
          <CardTitle className={`text-sm font-medium ${colorClasses[color].text}`}>{title}</CardTitle>
          <motion.div 
            className={`rounded-full p-2.5 ${colorClasses[color].iconBg} text-white shadow-md ${colorClasses[color].iconShadow}`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {icon}
          </motion.div>
        </CardHeader>
        <CardContent className="px-6 pb-6">
          <motion.div 
            className={`text-3xl font-bold ${colorClasses[color].text} mb-2`}
            initial={{ scale: 1 }}
            whileHover={{ scale: 1.03 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            {value}
          </motion.div>
          <div className="flex items-center space-x-2 text-sm text-muted-foreground mt-4">
            {trendValue !== undefined && (
              <>
                <span className={`flex items-center gap-0.5 ${trendColorClass} py-1 px-2 rounded-full text-xs`}>
                  <span className="text-base leading-none">{trendIcon}</span>
                  {trendValue}%
                </span>
                <span className="text-gray-500 text-xs">dalla settimana scorsa</span>
              </>
            )}
            {description && !trendValue && <span className="text-gray-500">{description}</span>}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default StatsCard;
