'use client';

import { Toaster } from 'react-hot-toast';

export function ToastProvider() {
  return (
    <Toaster
      position="top-right"
      toastOptions={{
        duration: 4000,
        style: {
          background: 'var(--background)',
          color: 'var(--foreground)',
          border: '1px solid var(--border)',
          padding: '12px 16px',
          borderRadius: '6px',
        },
        success: {
          style: {
            background: 'hsl(var(--success))', 
            color: 'white',
          },
          iconTheme: {
            primary: 'white',
            secondary: 'hsl(var(--success))',
          },
        },
        error: {
          style: {
            background: 'hsl(var(--destructive))',
            color: 'white',
          },
          iconTheme: {
            primary: 'white',
            secondary: 'hsl(var(--destructive))',
          },
        },
      }}
    />
  );
}
