.dashboard {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.dashboardContent {
  flex: 1;
  padding: 1.5rem 2rem;
  overflow-y: auto;
  width: 100%;
  margin: 0 auto;
}

.chatGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.25rem;
}

@media (max-width: 768px) {
  .chatGrid {
    grid-template-columns: 1fr;
  }
  
  .dashboardContent {
    padding: 1rem;
  }
}

.filterSection {
  margin-bottom: 1.5rem;
  background-color: var(--card-background);
  border-radius: 0.5rem;
  padding: 1rem;
}

.priority-high {
  color: var(--red);
}

.priority-medium {
  color: var(--accent);
}

.priority-low {
  color: var(--green);
}

.status-pending {
  background-color: var(--accent);
  color: white;
}

.status-in-progress {
  background-color: var(--blue);
  color: white;
}

.status-resolved {
  background-color: var(--green);
  color: white;
}

.containerCentered {
  width: 100%;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}
