'use client';

import React, { useContext, useEffect, useState } from 'react';
import { AuthContext } from '@/components/context/AuthContext';
import { useChatData, ChatFilters } from '@/components/context/ChatDataContext';
import { AdminRoute } from '@/components/auth/AdminRoute';
import SupportChatList from '@/components/dashboard/SupportChatList';
import ChatDashboardHeader from '@/components/dashboard/ChatDashboardHeader';
import Sidebar from '@/components/dashboard/Sidebar';
import ChatModal from '@/components/dashboard/ChatModal';
import FilterPanel from '@/components/dashboard/FilterPanel';
import ChatDetailView from '@/components/dashboard/ChatDetailView';
import styles from './dashboard.module.css';
import StatsSection from '@/components/dashboard/StatsSection';
import { useRouter, useSearchParams } from 'next/navigation';
import { SupportChat } from '@/components/dashboard/SupportChatList';

export default function DashboardPage() {
  const { isSidepanelOpen } = useContext(AuthContext);  const { 
    selectedChat, 
    setSelectedChat, 
    fetchChatById, 
    updateStatus
  } = useChatData();
  const router = useRouter();
  const searchParams = useSearchParams();  const status = searchParams.get('status');
  const priority = searchParams.get('priority');
  const assignedAgent = searchParams.get('agent');
  const sortBy = searchParams.get('sort');
  const chatId = searchParams.get('chat');

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Initialize based on window size
  useEffect(() => {
    const checkMobile = () => {
      const isMobileView = window.innerWidth < 768;
      setIsMobile(isMobileView);
    };
    
    // Check on component mount
    checkMobile();
    
    // Listen for window resize
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);  
  
  // Check for chat ID in URL  
  useEffect(() => {
    if (chatId) {
      const loadChat = async () => {
        try {
          const chat = await fetchChatById(chatId);
          if (!chat) {
            console.error('Chat not found');
            return;
          }
          setSelectedChat(chat);
          
          // On mobile, we navigate to the chat page
          // On desktop, we show the modal
          if (!isMobile) {
            setIsModalOpen(true);
          } else {
            router.push(`/dashboard/chat/${chatId}`);
          }
        } catch (error) {
          console.error('Failed to load chat:', error);
          // Handle error (maybe show a notification)
        }
      };
      
      loadChat();
    }
  }, [chatId, isMobile, router, fetchChatById, setSelectedChat]);
  
  // Handle chat selection
  const handleChatSelect = async (chat: SupportChat) => {
    // Use the summary from the list but get full details from the API
    try {
      setSelectedChat(chat); // Set immediately for better UX
      
      if (isMobile) {
        // On mobile, navigate to dedicated page
        router.push(`/dashboard/chat/${chat.id}`);
      } else {
        // On desktop, update URL and show modal
        const params = new URLSearchParams(searchParams.toString());
        params.set('chat', chat.id.toString());
        router.push(`/dashboard?${params.toString()}`, { scroll: false });
        setIsModalOpen(true);
        
        // Fetch full details in background
        const fullChat = await fetchChatById(chat.id.toString());
        if (fullChat) {
          setSelectedChat(fullChat);
        }
      }
    } catch (error) {
      console.error('Error handling chat selection:', error);
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
    
    // Remove chat ID from URL
    const params = new URLSearchParams(searchParams);
    params.delete('chat');
    router.push(`/dashboard?${params.toString()}`, { scroll: false });
  };  
    // Handle status update
  const handleStatusUpdate = async (chatId: number, newStatus: 'pending' | 'in_progress' | 'resolved') => {
    if (selectedChat) {
      try {
        // Optimistically update the UI
        setSelectedChat({
          ...selectedChat,
          status: newStatus
        });
        
        // Update via API using ChatDataContext
        const success = await updateStatus(chatId.toString(), newStatus);
        
        if (success) {
          // Broadcast the status change to update any listings
          window.dispatchEvent(new CustomEvent('chat-status-updated', {
            detail: { chatId, status: newStatus }
          }));
        } else {
          throw new Error('Failed to update status');
        }
      } catch (error) {
        console.error('Error updating chat status:', error);
        alert('Failed to update chat status. Please try again.');
        // Revert the optimistic update 
        const originalChat = await fetchChatById(chatId.toString());
        if (originalChat) {
          setSelectedChat(originalChat);
        }
      }
    }
  };
  // Initial filter state based on URL parameters
  const initialFilters: ChatFilters = {
    status: status as 'pending' | 'in_progress' | 'resolved' | undefined,
    priority: priority as 'low' | 'medium' | 'high' | 'urgent' | undefined,
    assigned_agent: assignedAgent || undefined,
    sort_by: (sortBy as 'latest' | 'priority') || 'latest'
  };  
  
  // Handle filter changes with proper type
  const handleFilterChange = (filters: ChatFilters) => {
    const params = new URLSearchParams(searchParams.toString());
    
    // Update the params based on the changed filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value as string);
      } else {
        params.delete(key);
      }
    });
    // Update the URL
    router.push(`/dashboard?${params.toString()}`, { scroll: false });
  };
    return (
    <AdminRoute>
      <div className="min-h-screen bg-[#f0f0f0] text-black flex flex-col">
        <ChatDashboardHeader />
        
        {/* Main content area */}
        <div className={`flex flex-1 ${styles.dashboard}`}>
          {/* Sidebar */}
          {isSidepanelOpen && <Sidebar />}
          
          {/* Main content with appropriate margin when sidebar is open */}
          <main 
            className={`flex-1 transition-all duration-300 ease-in-out ${
              isSidepanelOpen ? 'md:ml-72' : 'ml-0'
            } ${styles.dashboardContent}`}
            style={{ paddingTop: "calc(64px + 2rem)" }}
          >
            <div className="max-w-[1400px] w-full mx-auto px-4 md:px-8">
              {/* Container with consistent vertical spacing using flexbox gap */}
              <div className="flex flex-col gap-8 py-6">
                
                {/* Page Title */}
                <div>
                  <h1 className="text-2xl font-semibold text-[#113158]">Pannello di supporto</h1>
                </div>
                
                {/* Stats Section */}
                <div className="bg-[#f8f9fa] p-6 rounded-lg">
                  <StatsSection />
                </div>
                
                {/* Filter Panel */}
                <div>
                  <FilterPanel
                    onFilterChange={handleFilterChange}
                    currentFilters={initialFilters}
                  />
                </div>
                
                {/* Chat List */}
                <div className="bg-white p-8 rounded-lg shadow-md border border-[#febd49]/20">
                  <h2 className="text-xl font-medium text-[#113158] mb-6">Chat di supporto</h2>
                  <SupportChatList
                    onChatSelect={handleChatSelect}
                    initialFilters={initialFilters}
                  />
                </div>
                
              </div>
            </div>
          </main>
        </div>
        
        {/* Chat modal for desktop view */}
        {selectedChat && isModalOpen && (
          <ChatModal 
            isOpen={isModalOpen} 
            onClose={handleModalClose}
          >
            <ChatDetailView 
              chat={selectedChat}
              onBack={handleModalClose}
              onUpdateStatus={(status: 'pending' | 'in_progress' | 'resolved') => handleStatusUpdate(selectedChat.id, status)}
            />
          </ChatModal>        )}
      </div>
    </AdminRoute>
  );
}