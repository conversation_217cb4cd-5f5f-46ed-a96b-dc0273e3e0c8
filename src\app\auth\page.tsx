'use client';

import React, { useState, useContext, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Button from '@/components/buttons/Button';
import AuthLogo from '@/components/auth/AuthLogo';
import { AuthContext } from '@/components/context/AuthContext';
import { EyeIcon, EyeOffIcon } from 'lucide-react';
import Loader1 from '@/components/loaders/Loader1';
import Link from 'next/link';
import styles from './auth.module.css';
import { ClientOnly } from '@/components/auth/ClientOnly';

export default function AuthPage() {  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { login, isAuthenticated } = useContext(AuthContext);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirect = searchParams.get('redirect') || '/dashboard';
  const authError = searchParams.get('error');
  const authMessage = searchParams.get('message');
  useEffect(() => {
    if (isAuthenticated) {
      router.push(redirect);
    }
  }, [isAuthenticated, router, redirect]);

  const [, setIsMounted] = useState(false);
  
  useEffect(() => {
    setIsMounted(true);
    
    // Handle URL parameters for unauthorized access
    if (authError) {
      switch (authError) {
        case 'session_expired':
          setError('La tua sessione è scaduta. Accedi nuovamente.');
          break;
        case 'unauthorized':
          setError('Non hai i permessi necessari per accedere a questa risorsa.');
          break;
        case 'access_denied':
          setError('Accesso negato. Verifica le tue credenziali.');
          break;
        default:
          setError('Si è verificato un problema durante l\'accesso.');
      }
    }
    
    if (authMessage) {
      setStatusMessage(authMessage);
    }
  }, [authError, authMessage]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);
    
    try {
      if (!email.trim()) {
        setError('L\'email è obbligatoria');
        setIsLoading(false);
        return;
      }
      if (!password) {
        setError('La password è obbligatoria');
        setIsLoading(false);
        return;
      }    
      const result = await login(email, password);
      
      if (typeof result === 'boolean') {
        // Simple boolean result
        if (!result) {
          setError('Email o password non validi');
        }
      } else if (typeof result === 'object') {
        // Enhanced result with more details
        if (result.success) {
          // Login succeeded, redirect will be handled by the AuthContext
        } else {
          // Handle specific error cases
          switch(result.errorType) {
            case 'unauthorized':
              // For unauthorized errors, redirect to the unauthorized page
              if (result.redirectTo) {
                router.push(result.redirectTo);
                return; // Exit early to avoid setting error message
              }
              setError('Non hai i permessi necessari per accedere a questa area.');
              break;
            case 'invalid_credentials':
              setError('Email o password non validi.');
              break;
            case 'account_locked':
              setError('Account bloccato. Contatta l\'amministratore.');
              break;
            default:
              setError(result.message || 'Si è verificato un errore durante l\'accesso.');
          }
        }
      }
    } catch (err) {
      console.error('Errore di accesso:', err);
      setError('Si è verificato un errore. Riprova.');
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className={styles.authContainer}>
      <div className={styles.authCard}>
        <AuthLogo />
        <h1 className="text-2xl font-semibold text-center mb-2 text-[var(--text)]">
          Accedi al tuo account
        </h1>        <ClientOnly>
          {error && (
            <div className={styles.errorMessage}>
              {error}
            </div>
          )}
          {statusMessage && !error && (
            <div className={styles.statusMessage}>
              {statusMessage}
            </div>
          )}
        </ClientOnly>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className={styles.formGroup}>
            <label htmlFor="email" className="block text-sm font-medium text-[var(--text-faded)] mb-2">
              Email
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className={styles.inputField}
              placeholder="Inserisci la tua email"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="password" className="block text-sm font-medium text-[var(--text-faded)] mb-2">
              Password
            </label>
            <div className={styles.passwordField}>
              <input
                id="password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                autoComplete="current-password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={`${styles.inputField} pr-12`}
                placeholder="Inserisci la tua password"
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className={styles.passwordToggle}
              >
                {showPassword ? <EyeOffIcon size={20} /> : <EyeIcon size={20} />}
              </button>
            </div>
          </div>

          <div className={styles.rememberForgotRow}>
            <div>
              <Link href="/auth/reset-password" className={styles.forgotPassword}>
                Password dimenticata?
              </Link>
            </div>
          </div>

          <div>
            <ClientOnly>
              {isLoading ? (
                <div className="flex justify-center my-4">
                  <Loader1 className="scale-50" />
                </div>
              ) : (
                <button type="submit" className={styles.submitButton}>
                  <Button
                    onClick={() => {}}
                    text="Accedi"
                    backgroundColor="#113158" /* Explicitly set button background */
                    color="white" /* Explicitly set text color */
                    fontSize="16px"
                    border="none"
                  />
                </button>
              )}
            </ClientOnly>
          </div>
        </form>
      </div>
    </div>
  );
}