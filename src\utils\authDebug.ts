// Debug utility for tracking authentication state changes and preventing loops

interface AuthDebugLog {
  timestamp: number;
  event: string;
  data?: unknown;
}

class AuthDebugger {
  private logs: AuthDebugLog[] = [];
  private maxLogs = 50;
  private enabled = process.env.NODE_ENV === 'development';

  log(event: string, data?: unknown) {
    if (!this.enabled) return;

    const logEntry: AuthDebugLog = {
      timestamp: Date.now(),
      event,
      data
    };

    this.logs.push(logEntry);
    
    // Keep only the latest logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Log to console with timestamp
    const timeStr = new Date(logEntry.timestamp).toLocaleTimeString();
    console.log(`🔐 [${timeStr}] ${event}`, data || '');
  }

  checkForLoop(event: string, threshold = 5, timeWindow = 10000): boolean {
    if (!this.enabled) return false;

    const now = Date.now();
    const recentLogs = this.logs.filter(
      log => log.event === event && (now - log.timestamp) < timeWindow
    );

    if (recentLogs.length >= threshold) {
      console.warn(`🚨 Potential authentication loop detected for event: ${event}`);
      console.warn(`${recentLogs.length} occurrences in the last ${timeWindow}ms`);
      return true;
    }

    return false;
  }

  getLogs(): AuthDebugLog[] {
    return [...this.logs];
  }

  clear() {
    this.logs = [];
  }

  // Check if we're making too many API calls
  checkApiCallFrequency(endpoint: string, threshold = 3, timeWindow = 5000): boolean {
    return this.checkForLoop(`api:${endpoint}`, threshold, timeWindow);
  }
}

export const authDebugger = new AuthDebugger();

// Helper function to track API calls
export const trackApiCall = (endpoint: string, method: string = 'GET') => {
  authDebugger.log(`api:${endpoint}`, { method, endpoint });
  
  // Check for excessive API calls
  if (authDebugger.checkApiCallFrequency(endpoint)) {
    console.warn(`🚨 Too many API calls to ${endpoint}. Consider implementing caching or rate limiting.`);
  }
};

// Helper function to track authentication events
export const trackAuthEvent = (event: string, data?: Record<string, unknown>) => {
  authDebugger.log(`auth:${event}`, data);
  
  // Check for authentication loops
  if (authDebugger.checkForLoop(`auth:${event}`)) {
    console.warn(`🚨 Authentication loop detected for event: ${event}. This may cause performance issues.`);
  }
};

// Helper function to track WebSocket events
export const trackWebSocketEvent = (event: string, chatId?: string, data?: Record<string, unknown>) => {
  authDebugger.log(`ws:${event}`, { chatId, ...data });
  
  // Check for WebSocket reconnection loops
  if (event === 'reconnect' && chatId) {
    if (authDebugger.checkForLoop(`ws:reconnect:${chatId}`, 3, 30000)) {
      console.warn(`🚨 WebSocket reconnection loop detected for chat ${chatId}`);
    }
  }
};
