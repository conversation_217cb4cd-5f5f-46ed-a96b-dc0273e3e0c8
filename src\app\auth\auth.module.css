﻿.authContainer {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: var(--background);
}

.authCard {
  width: 100%;
  max-width: 450px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  padding: 2rem;
  transition: all 0.3s ease;
  animation: fadeIn 0.6s ease-out;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.formGroup {
  margin-bottom: 1.5rem;
}

.inputField {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  font-size: 1rem;
  transition: all 0.2s;
  background-color: white;
}

.inputField:hover {
  border-color: #cbd5e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.inputField:focus {
  outline: none;
  border-color: var(--accent);
  box-shadow: 0 0 0 2px rgba(252, 181, 31, 0.2);
}

.passwordField {
  position: relative;
}

.passwordToggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-faded);
}

.passwordToggle:focus {
  outline: none;
}

.errorMessage {
  background-color: #fee2e2;
  color: #ef4444;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.statusMessage {
  background-color: #e6f7ff;
  color: #0070f3;
  border: 1px solid #bae7ff;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.rememberForgotRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.25rem;
}

.checkboxContainer {
  display: flex;
  align-items: center;
}

.checkbox {
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  border: 1px solid #e2e8f0;
  accent-color: var(--accent);
}

.forgotPassword {
  color: #133157; /* Changed from var(--accent) to a visible blue color */
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: opacity 0.2s;
}

.forgotPassword:hover {
  opacity: 0.8;
  text-decoration: underline;
}

.submitButton {
  width: 100%;
}

@media (max-width: 480px) {
  .authCard {
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin: 0 0.5rem;
  }
  
  .inputField {
    font-size: 16px; /* Ensure no zoom on mobile */
    padding: 0.6rem 0.8rem;
  }
  
  .rememberForgotRow {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .forgotPassword {
    margin-top: 0.75rem;
  }
}
