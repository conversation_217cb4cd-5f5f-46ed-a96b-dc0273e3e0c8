import { useState, useEffect, useCallback, useRef } from 'react';
import { useAdminWebSocket } from '@/components/context/AdminWebSocketContext';
import { WebSocketConnectionState } from '@/services/websocketService';

interface UseAdminWebSocketChatProps {
  chatId: string | null;
  autoConnect?: boolean;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
}

interface UseAdminWebSocketChatReturn {
  connectionState: WebSocketConnectionState;
  messages: unknown[];
  sendMessage: (text: string) => void;
  sendTypingStart: () => void;
  sendTypingStop: () => void;
  connect: () => void;
  disconnect: () => void;
  isTyping: boolean;
  typingUsers: unknown[];
  unreadCount: number;
  markAsRead: () => void;
  uploadProgress: Map<string, unknown>;
}

export function useAdminWebSocketChat({ 
  chatId, 
  autoConnect = true,
  priority = 'medium'
}: UseAdminWebSocketChatProps): UseAdminWebSocketChatReturn {
  const {
    connectToChat,
    disconnectFromChat,
    getConnectionState,
    messages,
    sendMessage: adminSendMessage,
    sendTypingStart: adminSendTypingStart,
    sendTypingStop: adminSendTypingStop,
    typingUsers,
    unreadCounts,
    markAsRead: adminMarkAsRead,
    uploadProgress,
  } = useAdminWebSocket();

  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastTypingRef = useRef<Date | null>(null);

  // Get current chat data
  const connectionState = chatId ? getConnectionState(chatId) : 'disconnected';
  const chatMessages = chatId ? messages.get(chatId) || [] : [];
  const chatTypingUsers = chatId ? typingUsers.get(chatId) || [] : [];
  const unreadCount = chatId ? unreadCounts.get(chatId) || 0 : 0;
  const chatUploadProgress = chatId ? uploadProgress.get(chatId) || new Map() : new Map();

  // Auto-connect when chatId changes
  useEffect(() => {
    if (!chatId || !autoConnect) return;
    
    connectToChat(chatId, { priority, autoConnected: true });
    
    return () => {
      if (chatId) {
        disconnectFromChat(chatId);
      }
    };
  }, [chatId, autoConnect, priority, connectToChat, disconnectFromChat]);

  // Send message function
  const sendMessage = useCallback((text: string) => {
    if (chatId && text.trim()) {
      adminSendMessage(chatId, text);
      
      // Stop typing when sending message
      if (isTyping) {
        setIsTyping(false);
        adminSendTypingStop(chatId);
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
          typingTimeoutRef.current = null;
        }
      }
    }
  }, [chatId, adminSendMessage, isTyping, adminSendTypingStop]);

  // Typing indicators with debouncing
  const sendTypingStart = useCallback(() => {
    if (!chatId) return;
    
    const now = new Date();
    
    // Only send if we haven't sent typing in the last 3 seconds
    if (!lastTypingRef.current || now.getTime() - lastTypingRef.current.getTime() > 3000) {
      adminSendTypingStart(chatId);
      lastTypingRef.current = now;
    }
    
    setIsTyping(true);
    
    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Set new timeout to stop typing after 1 second of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      if (chatId) {
        adminSendTypingStop(chatId);
      }
      lastTypingRef.current = null;
    }, 1000);
  }, [chatId, adminSendTypingStart, adminSendTypingStop]);

  const sendTypingStop = useCallback(() => {
    if (!chatId) return;
    
    setIsTyping(false);
    adminSendTypingStop(chatId);
    
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
    
    lastTypingRef.current = null;
  }, [chatId, adminSendTypingStop]);

  // Manual connection controls
  const connect = useCallback(() => {
    if (chatId) {
      connectToChat(chatId, { priority });
    }
  }, [chatId, priority, connectToChat]);

  const disconnect = useCallback(() => {
    if (chatId) {
      disconnectFromChat(chatId);
    }
  }, [chatId, disconnectFromChat]);

  // Mark as read
  const markAsRead = useCallback(() => {
    if (chatId) {
      adminMarkAsRead(chatId);
    }
  }, [chatId, adminMarkAsRead]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return {
    connectionState,
    messages: chatMessages,
    sendMessage,
    sendTypingStart,
    sendTypingStop,
    connect,
    disconnect,
    isTyping,
    typingUsers: chatTypingUsers,
    unreadCount,
    markAsRead,
    uploadProgress: chatUploadProgress,
  };
}
