'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { 
  getSupportChats, 
  getChatMessages, 
  sendChatMessage, 
  updateChatStatus, 
  updateChatPriority 
} from '@/services/api';
import { SupportChat } from '@/components/dashboard/SupportChatList';
import { useAuth } from '@/components/context/AuthContext';

// Enhanced chat message type with proper typing
export interface ChatMessage {
  id: number;
  sender_type: 'user' | 'agent';
  content: string;
  timestamp: string;
  sender_name: string;
  status?: 'sending' | 'sent' | 'delivered' | 'read';
  attachments?: {
    id: number;
    file_name: string;
    file_url: string;
    content_type: string;
  }[];
}

// Filter options for chat queries
export interface ChatFilters {
  status?: 'pending' | 'in_progress' | 'resolved';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  assigned_agent?: string;
  sort_by?: 'latest' | 'priority';
}

// Cache entry structure for chats
interface ChatCacheEntry {
  data: SupportChat;
  timestamp: number;
  lastAccessed: number;
}

// Cache entry structure for messages
interface MessagesCacheEntry {
  data: ChatMessage[];
  timestamp: number;
  lastAccessed: number;
}

// Cache entry structure for chat lists
interface ChatListCacheEntry {
  data: SupportChat[];
  timestamp: number;
  lastAccessed: number;
  filters: ChatFilters;
}

// Request state tracking
interface RequestState {
  loading: boolean;
  error: string | null;
  timestamp: number;
}

// Context value interface
interface ChatDataContextValue {
  // Chat list management
  chats: SupportChat[];
  loadingChats: boolean;
  chatsError: string | null;
  
  // Individual chat management
  selectedChat: SupportChat | null;
  loadingChat: boolean;
  chatError: string | null;
  
  // Messages management
  messages: Map<string, ChatMessage[]>;
  loadingMessages: Map<string, boolean>;
  messagesError: Map<string, string | null>;
  
  // Actions
  fetchChats: (filters?: ChatFilters, forceRefresh?: boolean) => Promise<SupportChat[]>;
  fetchChatById: (chatId: string, forceRefresh?: boolean) => Promise<SupportChat | null>;
  fetchMessages: (chatId: string, forceRefresh?: boolean) => Promise<ChatMessage[]>;
  sendMessage: (chatId: string, message: string, attachments?: File[]) => Promise<boolean>;
  updateStatus: (chatId: string, status: 'pending' | 'in_progress' | 'resolved') => Promise<boolean>;
  updatePriority: (chatId: string, priority: 'low' | 'medium' | 'high' | 'urgent') => Promise<boolean>;
  
  // Cache management
  clearCache: () => void;
  invalidateChat: (chatId: string) => void;
  invalidateMessages: (chatId: string) => void;
  preloadChat: (chatId: string) => Promise<void>;
  
  // UI state
  setSelectedChat: (chat: SupportChat | null) => void;
}

// Default context value
const defaultContext: ChatDataContextValue = {
  chats: [],
  loadingChats: false,
  chatsError: null,
  selectedChat: null,
  loadingChat: false,
  chatError: null,
  messages: new Map(),
  loadingMessages: new Map(),
  messagesError: new Map(),
  fetchChats: async () => [],
  fetchChatById: async () => null,
  fetchMessages: async () => [],
  sendMessage: async () => false,
  updateStatus: async () => false,
  updatePriority: async () => false,
  clearCache: () => {},
  invalidateChat: () => {},
  invalidateMessages: () => {},
  preloadChat: async () => {},
  setSelectedChat: () => {},
};

// Create context
const ChatDataContext = createContext<ChatDataContextValue>(defaultContext);

// Cache configuration
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const STALE_TIME = 30 * 1000; // 30 seconds for background refresh
const MAX_CACHE_SIZE = 100; // Maximum number of cached items

// Provider component
export const ChatDataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isAdmin } = useAuth();
  
  // State management
  const [chats, setChats] = useState<SupportChat[]>([]);
  const [loadingChats, setLoadingChats] = useState(false);
  const [chatsError, setChatsError] = useState<string | null>(null);
  const [selectedChat, setSelectedChat] = useState<SupportChat | null>(null);
  const [loadingChat, setLoadingChat] = useState(false);
  const [chatError, setChatError] = useState<string | null>(null);
  const [messages, setMessages] = useState<Map<string, ChatMessage[]>>(new Map());
  const [loadingMessages, setLoadingMessages] = useState<Map<string, boolean>>(new Map());
  const [messagesError, setMessagesError] = useState<Map<string, string | null>>(new Map());
  
  // Cache storage
  const chatCacheRef = useRef<Map<string, ChatCacheEntry>>(new Map());
  const messagesCacheRef = useRef<Map<string, MessagesCacheEntry>>(new Map());
  const chatListCacheRef = useRef<Map<string, ChatListCacheEntry>>(new Map());
  
  // Request deduplication
  const pendingRequestsRef = useRef<Map<string, Promise<unknown>>>(new Map());
  const requestStatesRef = useRef<Map<string, RequestState>>(new Map());

  // Utility functions
  const isStale = (timestamp: number): boolean => {
    return Date.now() - timestamp > STALE_TIME;
  };

  const isExpired = (timestamp: number): boolean => {
    return Date.now() - timestamp > CACHE_DURATION;
  };

  const generateCacheKey = <T extends object>(type: string, params: T): string => {
    return `${type}_${JSON.stringify(params)}`;
  };

  const cleanupCache = useCallback(() => {
    // Cleanup chat cache
    for (const [key, entry] of chatCacheRef.current.entries()) {
      if (isExpired(entry.timestamp)) {
        chatCacheRef.current.delete(key);
      }
    }
    
    // Cleanup messages cache
    for (const [key, entry] of messagesCacheRef.current.entries()) {
      if (isExpired(entry.timestamp)) {
        messagesCacheRef.current.delete(key);
      }
    }
    
    // Cleanup chat list cache
    for (const [key, entry] of chatListCacheRef.current.entries()) {
      if (isExpired(entry.timestamp)) {
        chatListCacheRef.current.delete(key);
      }
    }
    
    // Limit cache size (LRU)
    if (chatCacheRef.current.size > MAX_CACHE_SIZE) {
      const sortedEntries = Array.from(chatCacheRef.current.entries())
        .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);
      
      for (let i = 0; i < sortedEntries.length - MAX_CACHE_SIZE; i++) {
        chatCacheRef.current.delete(sortedEntries[i][0]);
      }
    }
  }, []);

  // Fetch chats with caching and deduplication
  const fetchChats = useCallback(async (filters: ChatFilters = {}, forceRefresh = false): Promise<SupportChat[]> => {
    if (!isAuthenticated || !isAdmin) {
      return [];
    }

    const cacheKey = generateCacheKey('chats', filters);
    const cachedEntry = chatListCacheRef.current.get(cacheKey);
    
    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && cachedEntry && !isExpired(cachedEntry.timestamp)) {
      cachedEntry.lastAccessed = Date.now();
      setChats(cachedEntry.data);
      setChatsError(null);
      
      // Background refresh if stale
      if (isStale(cachedEntry.timestamp)) {
        fetchChats(filters, true).catch(console.error);
      }
      
      return cachedEntry.data;
    }

    // Check for pending request
    const pendingKey = `fetchChats_${cacheKey}`;
    if (pendingRequestsRef.current.has(pendingKey)) {
      return pendingRequestsRef.current.get(pendingKey) as Promise<SupportChat[]>;
    }

    setLoadingChats(true);
    setChatsError(null);

    const requestPromise = (async () => {
      try {
        const response = await getSupportChats(filters);
        
        if (response.status === 200 && response.data?.results) {
          const chatData = response.data.results;
          
          // Update cache
          chatListCacheRef.current.set(cacheKey, {
            data: chatData,
            timestamp: Date.now(),
            lastAccessed: Date.now(),
            filters
          });
          
          // Update state
          setChats(chatData);
          setChatsError(null);
          
          return chatData;
        } else {
          throw new Error('Failed to fetch chats');
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        setChatsError(errorMessage);
        console.error('Error fetching chats:', error);
        
        // Return cached data if available during error
        if (cachedEntry) {
          setChats(cachedEntry.data);
          return cachedEntry.data;
        }
        
        return [];
      } finally {
        setLoadingChats(false);
        pendingRequestsRef.current.delete(pendingKey);
      }
    })();

    pendingRequestsRef.current.set(pendingKey, requestPromise);
    return requestPromise;
  }, [isAuthenticated, isAdmin]);

  // Fetch individual chat by ID
  const fetchChatById = useCallback(async (chatId: string, forceRefresh = false): Promise<SupportChat | null> => {
    if (!isAuthenticated || !isAdmin) {
      return null;
    }

    const cachedEntry = chatCacheRef.current.get(chatId);
    
    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && cachedEntry && !isExpired(cachedEntry.timestamp)) {
      cachedEntry.lastAccessed = Date.now();
      setSelectedChat(cachedEntry.data);
      setChatError(null);
      
      // Background refresh if stale
      if (isStale(cachedEntry.timestamp)) {
        fetchChatById(chatId, true).catch(console.error);
      }
      
      return cachedEntry.data;
    }

    // Check for pending request
    const pendingKey = `fetchChatById_${chatId}`;
    if (pendingRequestsRef.current.has(pendingKey)) {
      return pendingRequestsRef.current.get(pendingKey) as Promise<SupportChat | null>;
    }

    setLoadingChat(true);
    setChatError(null);

    const requestPromise = (async () => {
      try {
        const chatData = await fetchChatById(chatId);
        
        if (chatData) {
          // Update cache
          chatCacheRef.current.set(chatId, {
            data: chatData,
            timestamp: Date.now(),
            lastAccessed: Date.now()
          });
          
          // Update state
          setSelectedChat(chatData);
          setChatError(null);
          
          return chatData;
        } else {
          throw new Error('Chat not found');
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        setChatError(errorMessage);
        console.error('Error fetching chat:', error);
        
        // Return cached data if available during error
        if (cachedEntry) {
          setSelectedChat(cachedEntry.data);
          return cachedEntry.data;
        }
        
        return null;
      } finally {
        setLoadingChat(false);
        pendingRequestsRef.current.delete(pendingKey);
      }
    })();

    pendingRequestsRef.current.set(pendingKey, requestPromise);
    return requestPromise;
  }, [isAuthenticated, isAdmin]);

  // Cache management functions - moved up to avoid reference before declaration
  const invalidateChat = useCallback((chatId: string) => {
    chatCacheRef.current.delete(chatId);
    
    // Also invalidate related chat list caches
    for (const [key] of chatListCacheRef.current.entries()) {
      chatListCacheRef.current.delete(key);
    }
  }, []);

  const invalidateMessages = useCallback((chatId: string) => {
    messagesCacheRef.current.delete(chatId);
  }, []);

  // Fetch messages for a chat
  const fetchMessages = useCallback(async (chatId: string, forceRefresh = false): Promise<ChatMessage[]> => {
    if (!isAuthenticated || !isAdmin) {
      return [];
    }

    const cachedEntry = messagesCacheRef.current.get(chatId);
    
    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && cachedEntry && !isExpired(cachedEntry.timestamp)) {
      cachedEntry.lastAccessed = Date.now();
      
      setMessages(prev => new Map(prev).set(chatId, cachedEntry.data));
      setMessagesError(prev => new Map(prev).set(chatId, null));
      
      // Background refresh if stale
      if (isStale(cachedEntry.timestamp)) {
        fetchMessages(chatId, true).catch(console.error);
      }
      
      return cachedEntry.data;
    }

    // Check for pending request
    const pendingKey = `fetchMessages_${chatId}`;
    if (pendingRequestsRef.current.has(pendingKey)) {
      return pendingRequestsRef.current.get(pendingKey) as Promise<ChatMessage[]>;
    }

    setLoadingMessages(prev => new Map(prev).set(chatId, true));
    setMessagesError(prev => new Map(prev).set(chatId, null));

    const requestPromise = (async () => {
      try {
        const response = await getChatMessages(chatId);
        
        if (response.status === 200 && response.data?.results) {
          interface ApiChatMessage {
            id: number;
            sender: string;
            message: string;
            created_at: string;
            user_name?: string;
            attachments?: Array<{
              id: number;
              file_name: string;
              file_url: string;
              content_type: string;
            }>;
          }
          
          const messageData: ChatMessage[] = response.data.results.map((msg: ApiChatMessage) => ({
            id: msg.id,
            sender_type: msg.sender === 'user' ? 'user' : 'agent',
            content: msg.message,
            timestamp: msg.created_at,
            sender_name: msg.user_name || 'User',
            attachments: msg.attachments || []
          }));
          
          // Update cache
          messagesCacheRef.current.set(chatId, {
            data: messageData,
            timestamp: Date.now(),
            lastAccessed: Date.now()
          });
          
          // Update state
          setMessages(prev => new Map(prev).set(chatId, messageData));
          setMessagesError(prev => new Map(prev).set(chatId, null));
          
          return messageData;
        } else {
          throw new Error('Failed to fetch messages');
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        setMessagesError(prev => new Map(prev).set(chatId, errorMessage));
        console.error('Error fetching messages:', error);
        
        // Return cached data if available during error
        if (cachedEntry) {
          setMessages(prev => new Map(prev).set(chatId, cachedEntry.data));
          return cachedEntry.data;
        }
        
        return [];
      } finally {
        setLoadingMessages(prev => new Map(prev).set(chatId, false));
        pendingRequestsRef.current.delete(pendingKey);
      }
    })();

    pendingRequestsRef.current.set(pendingKey, requestPromise);
    return requestPromise;
  }, [isAuthenticated, isAdmin]);

  // Send message with optimistic updates
  const sendMessage = useCallback(async (chatId: string, message: string, attachments?: File[]): Promise<boolean> => {
    if (!isAuthenticated || !isAdmin) {
      return false;
    }

    try {
      // Optimistic update
      const optimisticMessage: ChatMessage = {
        id: Date.now(), // Temporary ID
        sender_type: 'agent',
        content: message,
        timestamp: new Date().toISOString(),
        sender_name: 'Support Agent',
        status: 'sending',
        attachments: attachments?.map((file, index) => ({
          id: index,
          file_name: file.name,
          file_url: '#',
          content_type: file.type
        }))
      };

      setMessages(prev => {
        const current = prev.get(chatId) || [];
        return new Map(prev).set(chatId, [...current, optimisticMessage]);
      });

      const response = await sendChatMessage(chatId, message, attachments);
      
      if (response.status === 200 || response.status === 201) {
        // Invalidate cache to refetch fresh data
        invalidateMessages(chatId);
        await fetchMessages(chatId, true);
        return true;
      } else {
        // Remove optimistic message on failure
        setMessages(prev => {
          const current = prev.get(chatId) || [];
          return new Map(prev).set(chatId, current.filter(msg => msg.id !== optimisticMessage.id));
        });
        return false;
      }
    } catch (error) {
      console.error('Error sending message:', error);
      return false;
    }
  }, [isAuthenticated, isAdmin, fetchMessages, invalidateMessages]);

  // Update chat status
  const updateStatus = useCallback(async (chatId: string, status: 'pending' | 'in_progress' | 'resolved'): Promise<boolean> => {
    if (!isAuthenticated || !isAdmin) {
      return false;
    }

    try {
      const response = await updateChatStatus(parseInt(chatId), status);
      
      if (response.status === 200) {
        // Invalidate caches
        invalidateChat(chatId);
        
        // Update selected chat if it matches
        if (selectedChat && selectedChat.id.toString() === chatId) {
          setSelectedChat(prev => prev ? { ...prev, status } : null);
        }
        
        // Refresh chat list to reflect changes
        await fetchChats({}, true);
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating status:', error);
      return false;
    }
  }, [isAuthenticated, isAdmin, selectedChat, fetchChats, invalidateChat]);

  // Update chat priority
  const updatePriority = useCallback(async (chatId: string, priority: 'low' | 'medium' | 'high' | 'urgent'): Promise<boolean> => {
    if (!isAuthenticated || !isAdmin) {
      return false;
    }

    try {
      const response = await updateChatPriority(parseInt(chatId), priority);
      
      if (response.status === 200) {
        // Invalidate caches
        invalidateChat(chatId);
        
        // Update selected chat if it matches
        if (selectedChat && selectedChat.id.toString() === chatId) {
          setSelectedChat(prev => prev ? { ...prev, priority } : null);
        }
        
        // Refresh chat list to reflect changes
        await fetchChats({}, true);
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating priority:', error);
      return false;
    }
  }, [isAuthenticated, isAdmin, selectedChat, fetchChats, invalidateChat]);

  // Cache management functions
  const clearCache = useCallback(() => {
    chatCacheRef.current.clear();
    messagesCacheRef.current.clear();
    chatListCacheRef.current.clear();
    pendingRequestsRef.current.clear();
    requestStatesRef.current.clear();
  }, []);

  const preloadChat = useCallback(async (chatId: string) => {
    if (!chatCacheRef.current.has(chatId)) {
      await fetchChatById(chatId);
    }
    if (!messagesCacheRef.current.has(chatId)) {
      await fetchMessages(chatId);
    }
  }, [fetchChatById, fetchMessages]);

  // Cleanup interval
  useEffect(() => {
    const interval = setInterval(cleanupCache, 60 * 1000); // Every minute
    return () => clearInterval(interval);
  }, [cleanupCache]);

  // Clear cache when user logs out
  useEffect(() => {
    if (!isAuthenticated) {
      clearCache();
      setChats([]);
      setSelectedChat(null);
      setMessages(new Map());
    }
  }, [isAuthenticated, clearCache]);

  const contextValue: ChatDataContextValue = {
    chats,
    loadingChats,
    chatsError,
    selectedChat,
    loadingChat,
    chatError,
    messages,
    loadingMessages,
    messagesError,
    fetchChats,
    fetchChatById,
    fetchMessages,
    sendMessage,
    updateStatus,
    updatePriority,
    clearCache,
    invalidateChat,
    invalidateMessages,
    preloadChat,
    setSelectedChat,
  };

  return (
    <ChatDataContext.Provider value={contextValue}>
      {children}
    </ChatDataContext.Provider>
  );
};

// Hook to use the chat data context
export const useChatData = (): ChatDataContextValue => {
  const context = useContext(ChatDataContext);
  if (!context) {
    throw new Error('useChatData must be used within a ChatDataProvider');
  }
  return context;
};

export default ChatDataContext;
