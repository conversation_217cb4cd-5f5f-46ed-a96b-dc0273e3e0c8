/* Emoji picker styles */
.emoji-picker-container {
  position: relative;
  display: inline-block;
}

.emoji-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  color: #133157;
  opacity: 0.7;
  transition: all 0.2s ease;
  background: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
}

.emoji-button:hover {
  background-color: rgba(19, 49, 87, 0.08);
  opacity: 1;
}

.emoji-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(19, 49, 87, 0.2);
}

.emoji-picker {
  position: absolute;
  bottom: 40px;
  right: 0;
  width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.05);
  padding: 8px;
  z-index: 10;
  border: 1px solid rgba(19, 49, 87, 0.1);
  animation: scaleIn 0.2s ease forwards;
  transform-origin: bottom right;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.emoji-groups {
  display: flex;
  overflow-x: auto;
  padding: 4px;
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(19, 49, 87, 0.1);
}

.emoji-group-button {
  flex-shrink: 0;
  background: none;
  border: none;
  font-size: 18px;
  padding: 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-right: 2px;
}

.emoji-group-button:hover {
  background-color: rgba(19, 49, 87, 0.05);
}

.emoji-group-button.active {
  background-color: rgba(19, 49, 87, 0.1);
}

.emoji-list {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 4px;
  padding: 8px 4px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  cursor: pointer;
  background: transparent;
  border: none;
  transition: transform 0.1s ease, background-color 0.1s ease;
}

.emoji-item:hover {
  background-color: rgba(254, 189, 73, 0.15);
  transform: scale(1.1);
}

/* Mobile responsive design */
@media (max-width: 480px) {
  .emoji-picker {
    width: 280px;
    position: fixed;
    bottom: 70px;
    right: 10px;
  }

  .emoji-list {
    grid-template-columns: repeat(6, 1fr);
    max-height: 180px;
  }
}
