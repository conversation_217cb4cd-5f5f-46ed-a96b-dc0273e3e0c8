'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Clock, Check, CheckCheck } from 'lucide-react';

interface MessageStatusIconProps {
  status?: 'sending' | 'sent' | 'delivered' | 'read';
  className?: string;
  showTooltip?: boolean;
}

/**
 * Animated message status icon component that visualizes the current status of a message
 * with proper animations and accessibility features.
 */
export function MessageStatusIcon({ 
  status = 'sent', 
  className = '', 
  showTooltip = true 
}: MessageStatusIconProps) {
  // Animation variants for different status types
  const iconVariants = {
    sending: {
      scale: [0.8, 1.1, 0.8],
      opacity: [0.5, 0.8, 0.5],
      transition: {
        repeat: Infinity,
        duration: 1.5
      }
    },
    sent: {
      scale: [0, 1.2, 1],
      opacity: [0, 1, 1],
      transition: { 
        duration: 0.4,
        ease: "easeOut" 
      }
    },
    delivered: {
      x: [-5, 2, 0],
      opacity: [0, 0.7, 1],
      transition: { 
        duration: 0.4,
        ease: "easeOut"
      }
    },
    read: {
      scale: [0.8, 1.1, 1],
      color: ["#133157", "#febd49", "#10B981"],
      transition: { 
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  return (
    <div   className={`status-icon-container ${className}`}>
      <motion.div
        variants={iconVariants}
        animate={status}
        initial={status === 'sending' ? 'sending' : 'sent'}
        aria-label={`Message ${status}`}
      >
        {status === 'sending' && <Clock className="w-3 h-3" />}
        {status === 'sent' && <Check className="w-3 h-3" />}
        {status === 'delivered' && <CheckCheck className="w-3 h-3" />}
        {status === 'read' && <CheckCheck className="w-3 h-3" />}
      </motion.div>

      {showTooltip && (
        <div className="status-tooltip" role="tooltip">
          {status === 'sending' && 'Sending...'}
          {status === 'sent' && 'Sent'}
          {status === 'delivered' && 'Delivered'}
          {status === 'read' && 'Read'}
        </div>
      )}
    </div>
  );
}
