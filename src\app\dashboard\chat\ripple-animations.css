/* Ripple effect animations for better interactive feedback */

@keyframes ripple-effect {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(2.5);
    opacity: 0;
  }
}

.ripple {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  transform: scale(0);
  animation: ripple-effect 0.6s linear;
  pointer-events: none;
}

/* Dark mode ripple */
.dark .ripple {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Send button ripple styling */
.send-button .ripple {
  background-color: rgba(255, 255, 255, 0.6);
}

/* Attachment button ripple */
.attachment-button .ripple {
  background-color: rgba(19, 49, 87, 0.15);
}

/* RTL support for ripple */
[dir="rtl"] .ripple {
  animation-direction: reverse;
}

/* Button hover states with ripple readiness */
.send-button,
.attachment-button {
  position: relative;
  overflow: hidden; /* Keeps ripple within button bounds */
}

/* High contrast support */
@media (forced-colors: active) {
  .ripple {
    background-color: ButtonText;
    opacity: 0.3;
  }
}
