import React, { useEffect, useRef } from 'react';
import { useAuth } from '@/components/context/AuthContext';
import { useRouter } from 'next/navigation';

interface AdminRouteProps {
    children: React.ReactNode;
    fallbackUrl?: string;
}

/**
 * Higher-order component that protects routes requiring admin access.
 * Redirects non-admin users to unauthorized page or specified fallback URL.
 */
export const AdminRoute: React.FC<AdminRouteProps> = ({ 
    children, 
    fallbackUrl = '/unauthorized' 
}) => {
    const { isAuthenticated, isAdmin, isLoading } = useAuth();
    const router = useRouter();
    const redirectedRef = useRef(false);

    useEffect(() => {
        // Prevent multiple redirects
        if (redirectedRef.current) return;

        // Only redirect if we're done loading and don't have admin access
        if (!isLoading) {
            if (!isAuthenticated || !isAdmin) {
                redirectedRef.current = true;
                router.push(fallbackUrl);
            }
        }
    }, [isAuthenticated, isAdmin, isLoading, router, fallbackUrl]);

    // Show loading state while checking authentication
    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    // If authenticated and admin, render the protected content
    if (isAuthenticated && isAdmin) {
        return <>{children}</>;
    }

    // Return null while redirecting or if no access
    return null;
};

/**
 * Hook to check if current user has admin access
 * @returns boolean indicating if user is admin
 */
export const useAdminAccess = () => {
    const { checkAdminAccess } = useAuth();
    return checkAdminAccess();
};

/**
 * Hook to get admin status and user info
 * @returns object with admin status and user data
 */
export const useAdminStatus = () => {
    const { user, isAdmin, isAuthenticated } = useAuth();
    
    return {
        isAdmin,
        isAuthenticated,
        user,
        hasAdminAccess: isAuthenticated && isAdmin
    };
};
