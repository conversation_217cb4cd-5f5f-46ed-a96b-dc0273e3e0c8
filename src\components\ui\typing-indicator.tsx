'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface TypingUser {
  user_id: string;
  user_name: string;
  timestamp: Date;
}

interface TypingIndicatorProps {
  typingUsers: TypingUser[];
  className?: string;
  showUserNames?: boolean;
  maxDisplayUsers?: number;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  typingUsers,
  className = '',
  showUserNames = true,
  maxDisplayUsers = 3
}) => {
  if (typingUsers.length === 0) {
    return null;
  }

  const displayUsers = typingUsers.slice(0, maxDisplayUsers);
  const remainingCount = Math.max(0, typingUsers.length - maxDisplayUsers);

  const getTypingText = () => {
    if (!showUserNames) {
      return typingUsers.length === 1 ? 'Someone is typing...' : `${typingUsers.length} people are typing...`;
    }

    if (displayUsers.length === 1) {
      return `${displayUsers[0].user_name} is typing...`;
    } else if (displayUsers.length === 2) {
      return `${displayUsers[0].user_name} and ${displayUsers[1].user_name} are typing...`;
    } else if (displayUsers.length === 3 && remainingCount === 0) {
      return `${displayUsers[0].user_name}, ${displayUsers[1].user_name}, and ${displayUsers[2].user_name} are typing...`;
    } else {
      const names = displayUsers.map(u => u.user_name).join(', ');
      return `${names}${remainingCount > 0 ? ` and ${remainingCount} other${remainingCount > 1 ? 's' : ''}` : ''} are typing...`;
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className={`flex items-center gap-2 text-sm text-muted-foreground ${className}`}
      >
        <div className="flex gap-1">
          <motion.div
            className="w-2 h-2 bg-blue-500 rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
          <motion.div
            className="w-2 h-2 bg-blue-500 rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.2,
            }}
          />
          <motion.div
            className="w-2 h-2 bg-blue-500 rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.4,
            }}
          />
        </div>
        <span className="italic">{getTypingText()}</span>
      </motion.div>
    </AnimatePresence>
  );
};
