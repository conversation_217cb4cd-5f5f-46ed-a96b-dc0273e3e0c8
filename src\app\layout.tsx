import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/context/AuthContext";
import { ChatDataProvider } from "@/components/context/ChatDataContext";
import { ToastProvider } from "@/providers/ToastProvider";
import { WebSocketErrorHandler } from "@/components/ui/websocket-error-handler";
const inter = Inter({ subsets: ["latin"] });

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: process.env.NEXT_PUBLIC_PLATFORM_NAME,
  description: "Heibooky Support Chat",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className} suppressHydrationWarning>
        <AuthProvider>
          <ChatDataProvider>
            <ToastProvider />
            <WebSocketErrorHandler />
            {children}
          </ChatDataProvider>
        </AuthProvider>
      </body>
    </html>
  );
}