import Cookies from 'js-cookie';

// Check if code is running on client side
const isClient = typeof window !== 'undefined';

// Constants
const ACCESS_TOKEN_KEY = process.env.NEXT_PUBLIC_PLATFORM_TOKEN_NAME || 'access_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const TOKEN_EXPIRY_KEY = 'token_expiry';
const REFRESH_ATTEMPT_KEY = 'refresh_attempt';
const REFRESH_COOLDOWN_KEY = 'refresh_cooldown';

// Cookie options for refresh token
const COOKIE_OPTIONS = {
  secure: process.env.NODE_ENV === 'production', // Only use secure in production
  sameSite: 'strict' as const,
  path: '/',
  // HttpOnly cannot be set from client-side JavaScript
  // This would need to be set by the server
};

// In-memory storage for access token (not accessible via XSS)
let inMemoryToken: string | null = null;
let tokenExpiryTime: number | null = null;

// Refresh token cooldown and attempt tracking
const MAX_REFRESH_ATTEMPTS = 3; // Maximum number of refresh attempts before clearing tokens
const REFRESH_COOLDOWN_MS = 5000; // 5 seconds cooldown between refresh attempts

/**
 * Sets the access token in memory and its expiry time
 * @param token The access token
 * @param expiresIn Expiry time in seconds (default: 30 minutes)
 */
export const setAccessToken = (token: string, expiresIn: number = 14400): void => {
  inMemoryToken = token;

  // Calculate expiry time and store it
  const expiryTime = Date.now() + expiresIn * 1000;
  tokenExpiryTime = expiryTime;

  // Store expiry time in localStorage for persistence across page refreshes
  // This doesn't expose the token itself
  if (isClient) {
    localStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());
  }
};

/**
 * Gets the access token from memory
 * @returns The access token or null if not set
 */
export const getAccessToken = (): string | null => {
  return inMemoryToken;
};

/**
 * Sets the refresh token in a cookie
 * @param token The refresh token
 */
export const setRefreshToken = (token: string): void => {
  if (isClient) {
    Cookies.set(REFRESH_TOKEN_KEY, token, COOKIE_OPTIONS);
  }
};

/**
 * Gets the refresh token from the cookie
 * @returns The refresh token or null if not set
 */
export const getRefreshToken = (): string | null => {
  if (!isClient) {
    return null;
  }
  return Cookies.get(REFRESH_TOKEN_KEY) || null;
};

/**
 * Clears all authentication tokens
 */
export const clearTokens = (): void => {
  // Clear in-memory token
  inMemoryToken = null;
  tokenExpiryTime = null;

  // Clear cookie
  if (isClient) {
    Cookies.remove(REFRESH_TOKEN_KEY, { path: '/' });
  
    // Clear expiry time from localStorage
    localStorage.removeItem(TOKEN_EXPIRY_KEY);
  
    // For backward compatibility, also clear the old token storage
    localStorage.removeItem(String(ACCESS_TOKEN_KEY));
  
    // Clear refresh attempt tracking
    resetRefreshAttempts();
  }
};

/**
 * Checks if the access token is expired or close to expiring
 * @param thresholdSeconds Seconds before actual expiry to consider token as expired (default: 300 seconds = 5 minutes)
 * @returns True if token is expired or will expire soon
 */
export const isTokenExpired = (thresholdSeconds: number = 300): boolean => {
  if (!isClient) {
    // On server side, we can't check localStorage, so we assume token is not expired
    // The client will verify this later
    return false;
  }

  if (!tokenExpiryTime) {
    // Try to get from localStorage (for page refreshes)
    const storedExpiry = localStorage.getItem(TOKEN_EXPIRY_KEY);
    if (!storedExpiry) return true;

    tokenExpiryTime = parseInt(storedExpiry, 10);
  }

  // Check if current time is past expiry time minus threshold
  return Date.now() > tokenExpiryTime - thresholdSeconds * 1000;
};

/**
 * Initialize token from localStorage on app startup (for backward compatibility)
 * This should be called once when the app initializes
 */
export const initializeFromStorage = (): void => {
  // Only run on client side
  if (!isClient) {
    return;
  }
  
  // Try to get token from localStorage (old method)
  const oldToken = localStorage.getItem(String(ACCESS_TOKEN_KEY));
  if (oldToken) {
    setAccessToken(oldToken);
    // Remove from localStorage after moving to memory
    localStorage.removeItem(String(ACCESS_TOKEN_KEY));
  }

  // Check for expiry time
  const storedExpiry = localStorage.getItem(TOKEN_EXPIRY_KEY);
  if (storedExpiry) {
    tokenExpiryTime = parseInt(storedExpiry, 10);
  }
};

/**
 * Check if we have a valid token setup
 * @returns True if we have either a valid access token or a refresh token
 */
export const hasValidTokenSetup = (): boolean => {
  // Check if we have an access token that's not expired
  if (inMemoryToken && !isTokenExpired()) {
    return true;
  }

  // Check if we have a refresh token
  return !!getRefreshToken();
};

/**
 * Track refresh token attempts and enforce cooldown
 * @returns True if a refresh attempt is allowed, false if in cooldown or max attempts reached
 */
export const canAttemptTokenRefresh = (): boolean => {
  // Server-side always returns true as we can't check localStorage
  if (!isClient) {
    return true;
  }
  
  // Check if we're in cooldown period
  const cooldownUntil = localStorage.getItem(REFRESH_COOLDOWN_KEY);
  if (cooldownUntil) {
    const cooldownTime = parseInt(cooldownUntil, 10);
    if (Date.now() < cooldownTime) {
      // Still in cooldown period
      return false;
    }
    // Cooldown period has passed, clear it
    localStorage.removeItem(REFRESH_COOLDOWN_KEY);
  }

  // Check attempt count
  const attemptCount = localStorage.getItem(REFRESH_ATTEMPT_KEY);
  const attempts = attemptCount ? parseInt(attemptCount, 10) : 0;

  if (attempts >= MAX_REFRESH_ATTEMPTS) {
    // Too many attempts, enforce cooldown
    const cooldownUntil = Date.now() + REFRESH_COOLDOWN_MS;
    localStorage.setItem(REFRESH_COOLDOWN_KEY, cooldownUntil.toString());
    // Reset attempt counter
    localStorage.setItem(REFRESH_ATTEMPT_KEY, '0');
    return false;
  }

  return true;
};

/**
 * Increment the refresh attempt counter
 */
export const incrementRefreshAttempt = (): void => {
  if (!isClient) {
    return;
  }
  const attemptCount = localStorage.getItem(REFRESH_ATTEMPT_KEY);
  const attempts = attemptCount ? parseInt(attemptCount, 10) : 0;
  localStorage.setItem(REFRESH_ATTEMPT_KEY, (attempts + 1).toString());
};

/**
 * Reset the refresh attempt counter
 */
export const resetRefreshAttempts = (): void => {
  if (!isClient) {
    return;
  }
  localStorage.removeItem(REFRESH_ATTEMPT_KEY);
  localStorage.removeItem(REFRESH_COOLDOWN_KEY);
};
