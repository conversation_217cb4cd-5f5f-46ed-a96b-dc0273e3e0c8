'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FileIcon, X, CheckCircle2, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface UploadProgress {
  message_id: string;
  progress: number;
  file_name: string;
  status?: 'uploading' | 'completed' | 'error';
  error_message?: string;
}

interface FileUploadProgressProps {
  uploads: Map<string, UploadProgress>;
  onCancel?: (messageId: string) => void;
  className?: string;
}

export const FileUploadProgress: React.FC<FileUploadProgressProps> = ({
  uploads,
  onCancel,
  className = ''
}) => {
  const uploadArray = Array.from(uploads.values());

  if (uploadArray.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <AnimatePresence>
        {uploadArray.map((upload) => (
          <motion.div
            key={upload.message_id}
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border rounded-lg p-3 bg-white shadow-sm"
          >
            <div className="flex items-center gap-3">
              <div className="flex-shrink-0">
                {upload.status === 'completed' ? (
                  <CheckCircle2 className="w-5 h-5 text-green-500" />
                ) : upload.status === 'error' ? (
                  <AlertCircle className="w-5 h-5 text-red-500" />
                ) : (
                  <FileIcon className="w-5 h-5 text-blue-500" />
                )}
              </div>
              
              <div className="flex-grow min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <p className="text-sm font-medium truncate">
                    {upload.file_name}
                  </p>
                  
                  {upload.status === 'uploading' && onCancel && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onCancel(upload.message_id)}
                      className="h-6 w-6 p-0 text-muted-foreground hover:text-red-500"
                    >
                      <X size={12} />
                    </Button>
                  )}
                </div>
                
                {upload.status === 'error' && upload.error_message && (
                  <p className="text-xs text-red-600 mb-2">
                    {upload.error_message}
                  </p>
                )}
                
                {upload.status !== 'error' && (
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      className={`h-2 rounded-full ${
                        upload.status === 'completed' 
                          ? 'bg-green-500' 
                          : 'bg-blue-500'
                      }`}
                      initial={{ width: '0%' }}
                      animate={{ width: `${upload.progress}%` }}
                      transition={{ duration: 0.3, ease: 'easeOut' }}
                    />
                  </div>
                )}
                
                <div className="flex justify-between items-center mt-1">
                  <span className="text-xs text-muted-foreground">
                    {upload.status === 'completed' 
                      ? 'Upload completed' 
                      : upload.status === 'error'
                      ? 'Upload failed'
                      : `${Math.round(upload.progress)}% uploaded`
                    }
                  </span>
                  
                  {upload.status === 'uploading' && (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                      className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full"
                    />
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};
