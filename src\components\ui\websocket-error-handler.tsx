'use client';

import React, { useEffect } from 'react';
import { toast } from 'react-hot-toast';

interface WebSocketErrorDetail {
  chatId?: string;
  message: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  serverUrl?: string;
  error?: string;
}

export const WebSocketErrorHandler: React.FC = () => {
  useEffect(() => {
    // Handle WebSocket connection failures
    const handleConnectionFailed = (event: CustomEvent<WebSocketErrorDetail>) => {
      const { message, chatId, priority } = event.detail;
      
      // Show toast with appropriate priority styling
      const priorityEmoji = priority === 'urgent' ? '🚨' : priority === 'high' ? '⚠️' : '📡';
      
      toast.error(`${priorityEmoji} ${message}`, {
        id: `connection-failed-${chatId}`, // Prevent duplicate toasts
        duration: 5000,
        position: 'top-right',
        style: {
          backgroundColor: '#FEF2F2',
          borderColor: '#FCA5A5',
          color: '#B91C1C',
        },
      });
    };

    // Handle server unreachable events
    const handleServerUnreachable = (event: CustomEvent<WebSocketErrorDetail>) => {
      const { message, serverUrl } = event.detail;
      
      toast.error(`🔌 ${message}`, {
        id: 'server-unreachable', // Prevent duplicate toasts
        duration: 8000,
        position: 'top-right',
        style: {
          backgroundColor: '#FEF2F2',
          borderColor: '#FCA5A5',
          color: '#B91C1C',
        },
      });
      
      console.warn(`WebSocket server unreachable: ${serverUrl}`);
    };

    // Handle general WebSocket connection errors
    const handleConnectionError = (event: CustomEvent<WebSocketErrorDetail>) => {
      const { error, chatId, priority } = event.detail;
      
      const priorityEmoji = priority === 'urgent' ? '🚨' : priority === 'high' ? '⚠️' : '💥';
      
      toast.error(`${priorityEmoji} Chat connection error: ${error}`, {
        id: `connection-error-${chatId}`, // Prevent duplicate toasts
        duration: 6000,
        position: 'top-right',
        style: {
          backgroundColor: '#FEF2F2',
          borderColor: '#FCA5A5',
          color: '#B91C1C',
        },
      });
    };

    // Add event listeners
    window.addEventListener('websocket-connection-failed', handleConnectionFailed as EventListener);
    window.addEventListener('websocket-server-unreachable', handleServerUnreachable as EventListener);
    window.addEventListener('websocket-connection-error', handleConnectionError as EventListener);

    // Cleanup on unmount
    return () => {
      window.removeEventListener('websocket-connection-failed', handleConnectionFailed as EventListener);
      window.removeEventListener('websocket-server-unreachable', handleServerUnreachable as EventListener);
      window.removeEventListener('websocket-connection-error', handleConnectionError as EventListener);
    };
  }, []);

  // This component doesn't render anything, it just handles events
  return null;
};
