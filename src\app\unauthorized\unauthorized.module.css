.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--background) 0%, #f8f9fa 100%);
  padding: 1rem;
  position: relative;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(19, 49, 87, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(252, 181, 31, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

.content {
  width: 100%;
  max-width: 480px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background-color: white;
  border-radius: 20px;
  padding: 3rem 2rem;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.logoSection {
  margin-bottom: 2rem;
}

.errorIcon {
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.errorCode {
  font-size: 3.5rem;
  font-weight: 800;
  color: #dc2626;
  line-height: 1;
  margin-bottom: 0.5rem;
  position: relative;
  text-shadow: 0 2px 4px rgba(220, 38, 38, 0.1);
}

.errorCode::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #dc2626, #ef4444);
  border-radius: 2px;
}

.title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--blue);
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.alertBox {
  width: 100%;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(220, 38, 38, 0.05) 100%);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-left: 4px solid #ef4444;
  border-radius: 12px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  backdrop-filter: blur(10px);
}

.alertContent {
  display: flex;
  align-items: flex-start;
}

.alertIconWrapper {
  flex-shrink: 0;
  margin-right: 0.75rem;
  margin-top: 0.125rem;
}

.alertMessage {
  flex: 1;
}

.alertText {
  color: #b91c1c;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  margin: 0;
}

.description {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 90%;
}

.buttonContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.primaryButton {
  width: 100%;
}

.primaryButton a {
  display: block;
  width: 100%;
}

.secondaryButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  color: var(--blue);
  background-color: transparent;
  border: 2px solid var(--blue);
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  width: 100%;
}

.secondaryButton:hover {
  background-color: var(--blue);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(19, 49, 87, 0.15);
}

.secondaryButton:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 640px) {
  .content {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }
  
  .errorCode {
    font-size: 3rem;
  }
  
  .title {
    font-size: 1.5rem;
  }
  
  .description {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }
  
  .content {
    padding: 1.5rem 1rem;
  }
  
  .alertBox {
    padding: 1rem;
  }
}