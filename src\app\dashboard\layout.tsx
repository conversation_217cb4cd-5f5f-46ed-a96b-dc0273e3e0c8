'use client';

import React from 'react';
import { AdminWebSocketProvider } from '@/components/context/AdminWebSocketContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { AdminRoute } from '@/components/auth/AdminRoute';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ProtectedRoute>
      <AdminRoute>
        <AdminWebSocketProvider>
          {children}
        </AdminWebSocketProvider>
      </AdminRoute>
    </ProtectedRoute>
  );
}
